package club.projectessence.gameserver.instancemanager;

import club.projectessence.Config;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.serverpackets.ExShowScreenMessage;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

/**
 * Giftcode Manager với các biện pháp bảo mật chống cheat
 * <AUTHOR> Team
 */
public class GiftcodeManager {
    private static final Logger LOGGER = Logger.getLogger(GiftcodeManager.class.getName());
    
    // Security constants
    private static final int MAX_ATTEMPTS_PER_HOUR = 5;
    private static final int BLOCK_DURATION_MINUTES = 60;
    private static final Pattern VALID_CODE_PATTERN = Pattern.compile("^[A-Z0-9]{8,16}$");
    
    // SQL Queries
    private static final String CHECK_CODE_SQL = 
        "SELECT id, type, used_by_account, expires_date, is_active FROM giftcodes WHERE code = ? AND is_active = 1";
    
    private static final String USE_CODE_SQL = 
        "UPDATE giftcodes SET used_by_account = ?, used_date = NOW(), used_ip = ?, used_hwid = ? WHERE code = ? AND used_by_account IS NULL";
    
    private static final String CHECK_ACCOUNT_USAGE_SQL = 
        "SELECT COUNT(*) FROM giftcodes WHERE used_by_account = ?";
    
    private static final String LOG_ATTEMPT_SQL = 
        "INSERT INTO giftcode_logs (account_name, giftcode, action, ip_address, hwid, details) VALUES (?, ?, ?, ?, ?, ?)";
    
    private static final String CHECK_RATE_LIMIT_SQL = 
        "SELECT attempts, blocked_until FROM giftcode_rate_limit WHERE account_name = ? AND ip_address = ?";
    
    private static final String UPDATE_RATE_LIMIT_SQL = 
        "INSERT INTO giftcode_rate_limit (account_name, ip_address, attempts, last_attempt, blocked_until) " +
        "VALUES (?, ?, 1, NOW(), ?) ON DUPLICATE KEY UPDATE " +
        "attempts = attempts + 1, last_attempt = NOW(), blocked_until = ?";
    
    private static final String RESET_RATE_LIMIT_SQL = 
        "DELETE FROM giftcode_rate_limit WHERE account_name = ? AND ip_address = ?";

    private static class SingletonHolder {
        protected static final GiftcodeManager INSTANCE = new GiftcodeManager();
    }

    public static GiftcodeManager getInstance() {
        return SingletonHolder.INSTANCE;
    }

    private GiftcodeManager() {
        LOGGER.info("GiftcodeManager: Initialized with security features.");
    }

    /**
     * Sử dụng giftcode với các kiểm tra bảo mật
     */
    public GiftcodeResult useGiftcode(PlayerInstance player, String code) {
        if (player == null || code == null) {
            return new GiftcodeResult(false, "Thông tin không hợp lệ.");
        }

        final String accountName = player.getAccountName();
        final String ipAddress = player.getIPAddress();
        final String hwid = player.getHWID();
        
        // 1. Validate input format
        if (!isValidCodeFormat(code)) {
            logAttempt(accountName, code, "INVALID_FORMAT", ipAddress, hwid, "Invalid code format");
            return new GiftcodeResult(false, "Định dạng giftcode không hợp lệ.");
        }

        // 2. Check rate limiting
        if (isRateLimited(accountName, ipAddress)) {
            logAttempt(accountName, code, "RATE_LIMITED", ipAddress, hwid, "Rate limit exceeded");
            return new GiftcodeResult(false, "Bạn đã thử quá nhiều lần. Vui lòng thử lại sau 1 giờ.");
        }

        // 3. Check if account already used a giftcode
        if (hasAccountUsedGiftcode(accountName)) {
            updateRateLimit(accountName, ipAddress, false);
            logAttempt(accountName, code, "ALREADY_USED", ipAddress, hwid, "Account already used a giftcode");
            return new GiftcodeResult(false, "Tài khoản của bạn đã sử dụng giftcode rồi.");
        }

        // 4. Validate and use giftcode
        try (Connection con = DatabaseFactory.getConnection()) {
            // Check if code exists and is valid
            try (PreparedStatement ps = con.prepareStatement(CHECK_CODE_SQL)) {
                ps.setString(1, code.toUpperCase());
                try (ResultSet rs = ps.executeQuery()) {
                    if (!rs.next()) {
                        updateRateLimit(accountName, ipAddress, false);
                        logAttempt(accountName, code, "NOT_FOUND", ipAddress, hwid, "Code not found");
                        return new GiftcodeResult(false, "Giftcode không tồn tại.");
                    }

                    final int giftcodeId = rs.getInt("id");
                    final int type = rs.getInt("type");
                    final String usedByAccount = rs.getString("used_by_account");
                    final Timestamp expiresDate = rs.getTimestamp("expires_date");

                    // Check if already used
                    if (usedByAccount != null) {
                        updateRateLimit(accountName, ipAddress, false);
                        logAttempt(accountName, code, "ALREADY_USED_CODE", ipAddress, hwid, "Code already used by: " + usedByAccount);
                        return new GiftcodeResult(false, "Giftcode đã được sử dụng.");
                    }

                    // Check expiration
                    if (expiresDate != null && expiresDate.before(new Timestamp(System.currentTimeMillis()))) {
                        updateRateLimit(accountName, ipAddress, false);
                        logAttempt(accountName, code, "EXPIRED", ipAddress, hwid, "Code expired");
                        return new GiftcodeResult(false, "Giftcode đã hết hạn.");
                    }
                }
            }

            // Use the giftcode
            try (PreparedStatement ps = con.prepareStatement(USE_CODE_SQL)) {
                ps.setString(1, accountName);
                ps.setString(2, ipAddress);
                ps.setString(3, hwid);
                ps.setString(4, code.toUpperCase());
                
                int updated = ps.executeUpdate();
                if (updated == 0) {
                    updateRateLimit(accountName, ipAddress, false);
                    logAttempt(accountName, code, "CONCURRENT_USE", ipAddress, hwid, "Code used by another user concurrently");
                    return new GiftcodeResult(false, "Giftcode đã được sử dụng bởi người khác.");
                }
            }

            // Get code type for reward processing
            int rewardType = 0;
            try (PreparedStatement ps = con.prepareStatement(CHECK_CODE_SQL)) {
                ps.setString(1, code.toUpperCase());
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        rewardType = rs.getInt("type");
                    }
                }
            }

            // Apply rewards
            boolean rewardSuccess = applyReward(player, rewardType);
            if (rewardSuccess) {
                // Reset rate limit on success
                resetRateLimit(accountName, ipAddress);
                logAttempt(accountName, code, "SUCCESS", ipAddress, hwid, "Reward type: " + rewardType);
                
                String rewardMessage = getRewardMessage(rewardType);
                return new GiftcodeResult(true, "Sử dụng giftcode thành công! " + rewardMessage);
            } else {
                logAttempt(accountName, code, "REWARD_FAILED", ipAddress, hwid, "Failed to apply reward type: " + rewardType);
                return new GiftcodeResult(false, "Có lỗi xảy ra khi trao thưởng. Vui lòng liên hệ GM.");
            }

        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Database error in useGiftcode: " + e.getMessage(), e);
            updateRateLimit(accountName, ipAddress, false);
            logAttempt(accountName, code, "DB_ERROR", ipAddress, hwid, "Database error: " + e.getMessage());
            return new GiftcodeResult(false, "Lỗi hệ thống. Vui lòng thử lại sau.");
        }
    }

    /**
     * Validate code format
     */
    private boolean isValidCodeFormat(String code) {
        return code != null && VALID_CODE_PATTERN.matcher(code.toUpperCase()).matches();
    }

    /**
     * Check if account/IP is rate limited
     */
    private boolean isRateLimited(String accountName, String ipAddress) {
        try (Connection con = DatabaseFactory.getConnection();
             PreparedStatement ps = con.prepareStatement(CHECK_RATE_LIMIT_SQL)) {
            
            ps.setString(1, accountName);
            ps.setString(2, ipAddress);
            
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    int attempts = rs.getInt("attempts");
                    Timestamp blockedUntil = rs.getTimestamp("blocked_until");
                    
                    if (blockedUntil != null && blockedUntil.after(new Timestamp(System.currentTimeMillis()))) {
                        return true; // Still blocked
                    }
                    
                    return attempts >= MAX_ATTEMPTS_PER_HOUR;
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "Error checking rate limit: " + e.getMessage(), e);
        }
        return false;
    }

    /**
     * Update rate limiting
     */
    private void updateRateLimit(String accountName, String ipAddress, boolean success) {
        try (Connection con = DatabaseFactory.getConnection();
             PreparedStatement ps = con.prepareStatement(UPDATE_RATE_LIMIT_SQL)) {
            
            Timestamp blockUntil = success ? null : 
                new Timestamp(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(BLOCK_DURATION_MINUTES));
            
            ps.setString(1, accountName);
            ps.setString(2, ipAddress);
            ps.setTimestamp(3, blockUntil);
            ps.setTimestamp(4, blockUntil);
            ps.executeUpdate();
            
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "Error updating rate limit: " + e.getMessage(), e);
        }
    }

    /**
     * Reset rate limit on successful use
     */
    private void resetRateLimit(String accountName, String ipAddress) {
        try (Connection con = DatabaseFactory.getConnection();
             PreparedStatement ps = con.prepareStatement(RESET_RATE_LIMIT_SQL)) {
            
            ps.setString(1, accountName);
            ps.setString(2, ipAddress);
            ps.executeUpdate();
            
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "Error resetting rate limit: " + e.getMessage(), e);
        }
    }

    /**
     * Check if account has already used any giftcode
     */
    private boolean hasAccountUsedGiftcode(String accountName) {
        try (Connection con = DatabaseFactory.getConnection();
             PreparedStatement ps = con.prepareStatement(CHECK_ACCOUNT_USAGE_SQL)) {
            
            ps.setString(1, accountName);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "Error checking account usage: " + e.getMessage(), e);
        }
        return false;
    }

    /**
     * Apply reward based on type
     */
    private boolean applyReward(PlayerInstance player, int type) {
        try {
            switch (type) {
                case 1: // Premium 7 days
                    PremiumManager.getInstance().addPremiumTime(player.getObjectId(), 7, TimeUnit.DAYS, 1);
                    player.sendPacket(new ExShowScreenMessage("Bạn đã nhận được 7 ngày Premium!", 2, 5000, 0, true, true));
                    return true;
                    
                case 2: // 1000 Prime Points
                    int currentPoints = player.getPrimePoints();
                    int newPoints = Math.min(currentPoints + 1000, Integer.MAX_VALUE);
                    player.setPrimePoints(newPoints);
                    player.sendPacket(new ExShowScreenMessage("Bạn đã nhận được 1000 Prime Points!", 2, 5000, 0, true, true));
                    return true;
                    
                default:
                    LOGGER.warning("Unknown giftcode type: " + type);
                    return false;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error applying reward: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * Get reward description message
     */
    private String getRewardMessage(int type) {
        switch (type) {
            case 1:
                return "Bạn đã nhận được 7 ngày Premium!";
            case 2:
                return "Bạn đã nhận được 1000 Prime Points!";
            default:
                return "Phần thưởng đã được trao!";
        }
    }

    /**
     * Log giftcode attempt for audit
     */
    private void logAttempt(String accountName, String code, String action, String ipAddress, String hwid, String details) {
        try (Connection con = DatabaseFactory.getConnection();
             PreparedStatement ps = con.prepareStatement(LOG_ATTEMPT_SQL)) {
            
            ps.setString(1, accountName);
            ps.setString(2, code);
            ps.setString(3, action);
            ps.setString(4, ipAddress);
            ps.setString(5, hwid);
            ps.setString(6, details);
            ps.executeUpdate();
            
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "Error logging giftcode attempt: " + e.getMessage(), e);
        }
    }

    /**
     * Result class for giftcode operations
     */
    public static class GiftcodeResult {
        private final boolean success;
        private final String message;

        public GiftcodeResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }
    }
}
