# Hệ thống Giftcode L2J GVE - Hướng dẫn triển khai

## Tổng quan
Hệ thống giftcode hoàn chỉnh với các tính năng bảo mật cao, tích hợp với hệ thống Premium và PrimePoints hiện có.

## Các tính năng chính

### 1. **Loại Giftcode**
- **Type 1**: Premium 7 ngày (tích hợp với PremiumManager)
- **Type 2**: 1000 Prime Points (tích hợp với hệ thống PrimePoints)

### 2. **Giao diện người chơi**
- Button "Giftcode" trong Community Board home page
- Trang giftcode riêng với form nhập mã
- Hiển thị trạng thái tài khoản và thông tin phần thưởng
- Thông báo kết quả sử dụng giftcode

### 3. **Admin Commands**
- `//giftcode` - <PERSON><PERSON>
- `//giftcode_generate <type> <count> [expiry_days]` - Tạo giftcode
- `//giftcode_list [limit]` - Xem danh sách giftcode
- `//giftcode_export` - Export giftcode chưa sử dụng ra file
- `//giftcode_stats` - Thống kê sử dụng
- `//giftcode_delete <code>` - Xóa giftcode chưa sử dụng
- `//giftcode_cleanup` - Chạy cleanup thủ công ngay lập tức
- `//giftcode_cleanup_stats` - Xem thống kê cleanup
- `//giftcode_help` - Hướng dẫn chi tiết

## Biện pháp bảo mật chống cheat

### 1. **Giới hạn sử dụng**
- **Một giftcode per account**: Mỗi tài khoản chỉ được sử dụng 1 giftcode duy nhất
- **Rate limiting**: Tối đa 5 lần thử trong 1 giờ
- **Auto-block**: Tự động khóa 1 giờ khi vượt quá giới hạn

### 2. **Tracking và Audit**
- **IP Tracking**: Lưu IP address khi sử dụng giftcode
- **HWID Tracking**: Lưu Hardware ID để phát hiện multi-account
- **Complete Audit Log**: Ghi lại mọi hành động (thành công/thất bại)
- **GM Tracking**: Lưu thông tin GM tạo giftcode

### 3. **Validation và Security**
- **Input Validation**: Kiểm tra format giftcode (6 ký tự A-Z, 0-9)
- **SQL Injection Prevention**: Sử dụng PreparedStatement
- **Concurrent Use Protection**: Ngăn chặn sử dụng đồng thời
- **Expiry Date Support**: Hỗ trợ giftcode có thời hạn

### 4. **Database Security**
- **Unique Constraints**: Đảm bảo giftcode không trùng lặp
- **Indexed Queries**: Tối ưu hiệu suất truy vấn
- **Auto Cleanup**: Tự động xóa records cũ sau 30 ngày
- **Rate Limit Table**: Bảng riêng để quản lý rate limiting

## Cài đặt và triển khai

### Bước 1: Database Setup
```sql
-- Chạy file giftcode_database.sql để tạo các bảng cần thiết
mysql -u root -p your_database < giftcode_database.sql
```

### Bước 2: Copy Files
1. Copy `GiftcodeManager.java` vào `java/club/projectessence/gameserver/instancemanager/`
2. Copy `AdminGiftcode.java` vào `dist/game/data/scripts/handlers/admincommandhandlers/`
3. Copy `giftcode.html` vào `dist/game/data/html/CommunityBoard/KhoaCustom/`
4. Cập nhật `home.html`, `MainCBH.java`, và `AdminCommands.xml`

### Bước 3: Restart Server
Khởi động lại server để load các class và command mới.

### Bước 4: Test System
1. Tạo giftcode test: `//giftcode_generate 1 1`
2. Test sử dụng qua Community Board
3. Kiểm tra logs và database

## Hướng dẫn sử dụng cho GM

### Tạo Giftcode
```
//giftcode_generate 1 10        // 10 premium codes, không hết hạn
//giftcode_generate 2 5 30      // 5 prime point codes, hết hạn sau 30 ngày
```

### Quản lý Giftcode
```
//giftcode_list 20              // Xem 20 giftcode gần nhất
//giftcode_stats                // Xem thống kê
//giftcode_export               // Export codes chưa dùng ra file
//giftcode_delete CODE123       // Xóa code chưa sử dụng
//giftcode_cleanup              // Chạy cleanup thủ công
//giftcode_cleanup_stats        // Xem thống kê cleanup
```

### Auto Cleanup System
- **Tự động chạy**: Mỗi 24 giờ, bắt đầu sau 1 giờ khi server khởi động
- **Cleanup Rules**:
  - Giftcode đã sử dụng: Xóa sau 30 ngày
  - Audit logs: Xóa sau 30 ngày
  - Rate limit records: Xóa sau 7 ngày
- **Manual Commands**:
  - `//giftcode_cleanup` - Chạy cleanup ngay lập tức
  - `//giftcode_cleanup_stats` - Xem số records sẽ bị xóa

### Export Files
- File export được lưu trong thư mục `log/`
- Format: `giftcodes_export_YYYYMMDD_HHMMSS.txt`
- Phân loại theo type và có tổng số

## Monitoring và Maintenance

### 1. **Log Files**
- Check `log/` folder cho export files
- Monitor server logs cho errors
- Review audit logs trong database

### 2. **Database Maintenance**

**⚠️ Lưu ý**: Hệ thống đã có auto cleanup, không cần chạy manual trừ khi cần thiết.

```sql
-- Kiểm tra records sẽ bị cleanup (chỉ để xem)
SELECT 'Used Codes' as type, COUNT(*) as count
FROM giftcodes
WHERE used_by_account IS NOT NULL AND used_date < DATE_SUB(NOW(), INTERVAL 30 DAY)
UNION ALL
SELECT 'Old Logs' as type, COUNT(*) as count
FROM giftcode_logs
WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY)
UNION ALL
SELECT 'Old Rate Limits' as type, COUNT(*) as count
FROM giftcode_rate_limit
WHERE last_attempt < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- Thống kê sử dụng
SELECT
    type,
    COUNT(*) as total,
    SUM(CASE WHEN used_by_account IS NOT NULL THEN 1 ELSE 0 END) as used,
    SUM(CASE WHEN used_by_account IS NULL THEN 1 ELSE 0 END) as unused
FROM giftcodes
WHERE is_active = 1
GROUP BY type;

-- Manual cleanup (chỉ khi cần thiết)
-- DELETE FROM giftcodes WHERE used_by_account IS NOT NULL AND used_date < DATE_SUB(NOW(), INTERVAL 30 DAY);
-- DELETE FROM giftcode_logs WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY);
-- DELETE FROM giftcode_rate_limit WHERE last_attempt < DATE_SUB(NOW(), INTERVAL 7 DAY);
```

### 3. **Security Monitoring**
```sql
-- Kiểm tra accounts có nhiều attempts
SELECT account_name, ip_address, attempts, last_attempt 
FROM giftcode_rate_limit 
WHERE attempts >= 3 
ORDER BY attempts DESC;

-- Kiểm tra suspicious activities
SELECT account_name, COUNT(*) as failed_attempts 
FROM giftcode_logs 
WHERE action IN ('FAILED', 'INVALID', 'RATE_LIMITED') 
AND timestamp > DATE_SUB(NOW(), INTERVAL 1 DAY)
GROUP BY account_name 
HAVING failed_attempts > 5;
```

## Troubleshooting

### Lỗi thường gặp

1. **"Database error"**
   - Kiểm tra database connection
   - Verify tables đã được tạo
   - Check permissions

2. **"Rate limit exceeded"**
   - Normal behavior, chờ 1 giờ
   - GM có thể reset: `DELETE FROM giftcode_rate_limit WHERE account_name = 'account_name';`

3. **"Giftcode not found"**
   - Kiểm tra code đã được tạo
   - Verify code format (6 chars, A-Z, 0-9)
   - Check expiry date

4. **"Account already used giftcode"**
   - Mỗi account chỉ dùng được 1 code
   - Check database: `SELECT * FROM giftcodes WHERE used_by_account = 'account_name';`

### Performance Tips

1. **Database Indexing**
   - Indexes đã được tạo sẵn trong schema
   - Monitor query performance

2. **Rate Limiting Cleanup**
   - Tự động cleanup old records
   - Hoặc setup cron job

3. **Log Rotation**
   - Định kỳ archive old logs
   - Prevent database bloat

## Tùy chỉnh và mở rộng

### Thêm loại giftcode mới
1. Cập nhật `applyReward()` method trong `GiftcodeManager.java`
2. Thêm case mới trong `generateGiftcodes()` method
3. Update help text và documentation

### Thay đổi rate limits
Sửa constants trong `GiftcodeManager.java`:
```java
private static final int MAX_ATTEMPTS_PER_HOUR = 5;
private static final int BLOCK_DURATION_MINUTES = 60;
```

### Custom validation
Sửa `VALID_CODE_PATTERN` trong `GiftcodeManager.java`:
```java
private static final Pattern VALID_CODE_PATTERN = Pattern.compile("^[A-Z0-9]{6}$");
```

## Bảo mật bổ sung (Khuyến nghị)

### 1. **Server-side**
- Thường xuyên backup database
- Monitor unusual patterns
- Implement IP whitelist cho admin commands
- Use HTTPS cho web interfaces

### 2. **Operational Security**
- Không share giftcode publicly
- Distribute through secure channels
- Monitor usage patterns
- Regular security audits

### 3. **Advanced Features** (Tương lai)
- Two-factor authentication cho admin
- Encrypted giftcode storage
- Advanced fraud detection
- Integration với anti-cheat systems

---

**Lưu ý**: Hệ thống này đã được thiết kế với focus cao về bảo mật. Tuy nhiên, luôn monitor và update theo threats mới.
