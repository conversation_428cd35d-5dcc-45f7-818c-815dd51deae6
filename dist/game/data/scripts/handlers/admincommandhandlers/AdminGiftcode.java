package handlers.admincommandhandlers;

import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.handler.IAdminCommandHandler;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.util.BuilderUtil;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.StringTokenizer;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Admin Command Handler for Giftcode System
 * <AUTHOR> Team
 */
public class AdminGiftcode implements IAdminCommandHandler {
    private static final Logger LOGGER = Logger.getLogger(AdminGiftcode.class.getName());
    
    private static final String[] ADMIN_COMMANDS = {
        "admin_giftcode",
        "admin_giftcode_generate",
        "admin_giftcode_list",
        "admin_giftcode_export",
        "admin_giftcode_stats",
        "admin_giftcode_delete",
        "admin_giftcode_help"
    };

    // SQL Queries
    private static final String INSERT_GIFTCODE_SQL = 
        "INSERT INTO giftcodes (code, type, created_by_gm, expires_date) VALUES (?, ?, ?, ?)";
    
    private static final String LIST_GIFTCODES_SQL = 
        "SELECT code, type, created_date, created_by_gm, used_by_account, used_date, expires_date " +
        "FROM giftcodes ORDER BY created_date DESC LIMIT ?";
    
    private static final String UNUSED_GIFTCODES_SQL = 
        "SELECT code, type, created_date, expires_date FROM giftcodes " +
        "WHERE used_by_account IS NULL AND is_active = 1 " +
        "AND (expires_date IS NULL OR expires_date > NOW()) ORDER BY type, created_date";
    
    private static final String STATS_SQL = 
        "SELECT " +
        "COUNT(*) as total, " +
        "SUM(CASE WHEN used_by_account IS NOT NULL THEN 1 ELSE 0 END) as used, " +
        "SUM(CASE WHEN used_by_account IS NULL AND is_active = 1 THEN 1 ELSE 0 END) as unused, " +
        "SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as premium_codes, " +
        "SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as primepoint_codes " +
        "FROM giftcodes";
    
    private static final String DELETE_GIFTCODE_SQL = 
        "UPDATE giftcodes SET is_active = 0 WHERE code = ? AND used_by_account IS NULL";

    @Override
    public boolean useAdminCommand(String command, PlayerInstance activeChar) {
        if (activeChar == null) {
            return false;
        }

        StringTokenizer st = new StringTokenizer(command, " ");
        String actualCommand = st.nextToken();

        switch (actualCommand.toLowerCase()) {
            case "admin_giftcode":
                showMainMenu(activeChar);
                break;
                
            case "admin_giftcode_generate":
                if (st.countTokens() < 2) {
                    BuilderUtil.sendSysMessage(activeChar, "Usage: //giftcode_generate <type> <count> [expiry_days]");
                    BuilderUtil.sendSysMessage(activeChar, "Type: 1=Premium 7 days, 2=1000 Prime Points");
                    return false;
                }
                
                try {
                    int type = Integer.parseInt(st.nextToken());
                    int count = Integer.parseInt(st.nextToken());
                    int expiryDays = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
                    
                    if (type < 1 || type > 2) {
                        BuilderUtil.sendSysMessage(activeChar, "Invalid type. Use 1 for Premium or 2 for Prime Points.");
                        return false;
                    }
                    
                    if (count < 1 || count > 100) {
                        BuilderUtil.sendSysMessage(activeChar, "Count must be between 1 and 100.");
                        return false;
                    }
                    
                    generateGiftcodes(activeChar, type, count, expiryDays);
                } catch (NumberFormatException e) {
                    BuilderUtil.sendSysMessage(activeChar, "Invalid number format.");
                }
                break;
                
            case "admin_giftcode_list":
                int limit = 20;
                if (st.hasMoreTokens()) {
                    try {
                        limit = Integer.parseInt(st.nextToken());
                        limit = Math.min(limit, 100); // Max 100
                    } catch (NumberFormatException e) {
                        limit = 20;
                    }
                }
                listGiftcodes(activeChar, limit);
                break;
                
            case "admin_giftcode_export":
                exportUnusedGiftcodes(activeChar);
                break;
                
            case "admin_giftcode_stats":
                showStats(activeChar);
                break;
                
            case "admin_giftcode_delete":
                if (!st.hasMoreTokens()) {
                    BuilderUtil.sendSysMessage(activeChar, "Usage: //giftcode_delete <code>");
                    return false;
                }
                String codeToDelete = st.nextToken().toUpperCase();
                deleteGiftcode(activeChar, codeToDelete);
                break;
                
            case "admin_giftcode_help":
                showHelp(activeChar);
                break;
                
            default:
                return false;
        }
        
        return true;
    }

    private void showMainMenu(PlayerInstance activeChar) {
        BuilderUtil.sendSysMessage(activeChar, "=== Giftcode Management System ===");
        BuilderUtil.sendSysMessage(activeChar, "//giftcode_generate <type> <count> [expiry_days] - Generate giftcodes");
        BuilderUtil.sendSysMessage(activeChar, "//giftcode_list [limit] - List recent giftcodes");
        BuilderUtil.sendSysMessage(activeChar, "//giftcode_export - Export unused codes to file");
        BuilderUtil.sendSysMessage(activeChar, "//giftcode_stats - Show statistics");
        BuilderUtil.sendSysMessage(activeChar, "//giftcode_delete <code> - Delete unused code");
        BuilderUtil.sendSysMessage(activeChar, "//giftcode_help - Show detailed help");
    }

    private void generateGiftcodes(PlayerInstance activeChar, int type, int count, int expiryDays) {
        List<String> generatedCodes = new ArrayList<>();
        Timestamp expiryDate = null;
        
        if (expiryDays > 0) {
            expiryDate = new Timestamp(System.currentTimeMillis() + TimeUnit.DAYS.toMillis(expiryDays));
        }

        try (Connection con = DatabaseFactory.getConnection();
             PreparedStatement ps = con.prepareStatement(INSERT_GIFTCODE_SQL)) {
            
            for (int i = 0; i < count; i++) {
                String code = generateUniqueCode();
                
                ps.setString(1, code);
                ps.setInt(2, type);
                ps.setString(3, activeChar.getName());
                ps.setTimestamp(4, expiryDate);
                
                try {
                    ps.executeUpdate();
                    generatedCodes.add(code);
                } catch (SQLException e) {
                    if (e.getMessage().contains("Duplicate entry")) {
                        i--; // Retry with new code
                        continue;
                    }
                    throw e;
                }
            }
            
            String typeDesc = (type == 1) ? "Premium 7 days" : "1000 Prime Points";
            String expiryDesc = (expiryDays > 0) ? " (expires in " + expiryDays + " days)" : " (no expiry)";
            
            BuilderUtil.sendSysMessage(activeChar, "Generated " + generatedCodes.size() + " giftcodes for " + typeDesc + expiryDesc);
            BuilderUtil.sendSysMessage(activeChar, "Codes: " + String.join(", ", generatedCodes));
            
            LOGGER.info("GM " + activeChar.getName() + " generated " + count + " giftcodes of type " + type);
            
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error generating giftcodes: " + e.getMessage(), e);
            BuilderUtil.sendSysMessage(activeChar, "Error generating giftcodes: " + e.getMessage());
        }
    }

    private String generateUniqueCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        
        // Generate 12 character code: XXXX-XXXX-XXXX format (without dashes in DB)
        for (int i = 0; i < 12; i++) {
            code.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return code.toString();
    }

    private void listGiftcodes(PlayerInstance activeChar, int limit) {
        try (Connection con = DatabaseFactory.getConnection();
             PreparedStatement ps = con.prepareStatement(LIST_GIFTCODES_SQL)) {
            
            ps.setInt(1, limit);
            
            try (ResultSet rs = ps.executeQuery()) {
                BuilderUtil.sendSysMessage(activeChar, "=== Recent Giftcodes (Last " + limit + ") ===");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                
                int count = 0;
                while (rs.next()) {
                    count++;
                    String code = rs.getString("code");
                    int type = rs.getInt("type");
                    Timestamp created = rs.getTimestamp("created_date");
                    String createdBy = rs.getString("created_by_gm");
                    String usedBy = rs.getString("used_by_account");
                    Timestamp used = rs.getTimestamp("used_date");
                    Timestamp expires = rs.getTimestamp("expires_date");
                    
                    String typeDesc = (type == 1) ? "Premium" : "PrimePoints";
                    String status = (usedBy != null) ? "USED by " + usedBy : "UNUSED";
                    String expiryInfo = (expires != null) ? " (expires: " + sdf.format(expires) + ")" : "";
                    
                    BuilderUtil.sendSysMessage(activeChar, count + ". " + code + " [" + typeDesc + "] " + status + 
                        " | Created: " + sdf.format(created) + " by " + createdBy + expiryInfo);
                }
                
                if (count == 0) {
                    BuilderUtil.sendSysMessage(activeChar, "No giftcodes found.");
                }
            }
            
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error listing giftcodes: " + e.getMessage(), e);
            BuilderUtil.sendSysMessage(activeChar, "Error listing giftcodes: " + e.getMessage());
        }
    }

    private void exportUnusedGiftcodes(PlayerInstance activeChar) {
        try (Connection con = DatabaseFactory.getConnection();
             PreparedStatement ps = con.prepareStatement(UNUSED_GIFTCODES_SQL)) {
            
            List<String> premiumCodes = new ArrayList<>();
            List<String> primepointCodes = new ArrayList<>();
            
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    String code = rs.getString("code");
                    int type = rs.getInt("type");
                    
                    if (type == 1) {
                        premiumCodes.add(code);
                    } else if (type == 2) {
                        primepointCodes.add(code);
                    }
                }
            }
            
            // Create export file
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(System.currentTimeMillis());
            String filename = "giftcodes_export_" + timestamp + ".txt";
            File exportFile = new File("log/" + filename);
            
            // Ensure log directory exists
            exportFile.getParentFile().mkdirs();
            
            try (FileWriter writer = new FileWriter(exportFile)) {
                writer.write("=== GIFTCODE EXPORT ===\n");
                writer.write("Export Date: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(System.currentTimeMillis()) + "\n");
                writer.write("Exported by GM: " + activeChar.getName() + "\n\n");
                
                writer.write("=== PREMIUM 7 DAYS CODES (" + premiumCodes.size() + ") ===\n");
                for (String code : premiumCodes) {
                    writer.write(code + "\n");
                }
                
                writer.write("\n=== 1000 PRIME POINTS CODES (" + primepointCodes.size() + ") ===\n");
                for (String code : primepointCodes) {
                    writer.write(code + "\n");
                }
                
                writer.write("\n=== TOTAL: " + (premiumCodes.size() + primepointCodes.size()) + " codes ===\n");
            }
            
            BuilderUtil.sendSysMessage(activeChar, "Exported " + (premiumCodes.size() + primepointCodes.size()) + 
                " unused giftcodes to: " + filename);
            BuilderUtil.sendSysMessage(activeChar, "Premium codes: " + premiumCodes.size() + 
                ", Prime Point codes: " + primepointCodes.size());
            
            LOGGER.info("GM " + activeChar.getName() + " exported giftcodes to " + filename);
            
        } catch (SQLException | IOException e) {
            LOGGER.log(Level.SEVERE, "Error exporting giftcodes: " + e.getMessage(), e);
            BuilderUtil.sendSysMessage(activeChar, "Error exporting giftcodes: " + e.getMessage());
        }
    }

    private void showStats(PlayerInstance activeChar) {
        try (Connection con = DatabaseFactory.getConnection();
             PreparedStatement ps = con.prepareStatement(STATS_SQL)) {
            
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    int total = rs.getInt("total");
                    int used = rs.getInt("used");
                    int unused = rs.getInt("unused");
                    int premiumCodes = rs.getInt("premium_codes");
                    int primepointCodes = rs.getInt("primepoint_codes");
                    
                    BuilderUtil.sendSysMessage(activeChar, "=== Giftcode Statistics ===");
                    BuilderUtil.sendSysMessage(activeChar, "Total codes: " + total);
                    BuilderUtil.sendSysMessage(activeChar, "Used codes: " + used + " (" + 
                        (total > 0 ? String.format("%.1f", (used * 100.0 / total)) : "0") + "%)");
                    BuilderUtil.sendSysMessage(activeChar, "Unused codes: " + unused);
                    BuilderUtil.sendSysMessage(activeChar, "Premium codes: " + premiumCodes);
                    BuilderUtil.sendSysMessage(activeChar, "Prime Point codes: " + primepointCodes);
                }
            }
            
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting stats: " + e.getMessage(), e);
            BuilderUtil.sendSysMessage(activeChar, "Error getting statistics: " + e.getMessage());
        }
    }

    private void deleteGiftcode(PlayerInstance activeChar, String code) {
        try (Connection con = DatabaseFactory.getConnection();
             PreparedStatement ps = con.prepareStatement(DELETE_GIFTCODE_SQL)) {
            
            ps.setString(1, code);
            int deleted = ps.executeUpdate();
            
            if (deleted > 0) {
                BuilderUtil.sendSysMessage(activeChar, "Giftcode " + code + " has been deleted.");
                LOGGER.info("GM " + activeChar.getName() + " deleted giftcode: " + code);
            } else {
                BuilderUtil.sendSysMessage(activeChar, "Giftcode not found or already used.");
            }
            
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error deleting giftcode: " + e.getMessage(), e);
            BuilderUtil.sendSysMessage(activeChar, "Error deleting giftcode: " + e.getMessage());
        }
    }

    private void showHelp(PlayerInstance activeChar) {
        BuilderUtil.sendSysMessage(activeChar, "=== Giftcode System Help ===");
        BuilderUtil.sendSysMessage(activeChar, "");
        BuilderUtil.sendSysMessage(activeChar, "GENERATE CODES:");
        BuilderUtil.sendSysMessage(activeChar, "//giftcode_generate 1 10 - Generate 10 premium codes (no expiry)");
        BuilderUtil.sendSysMessage(activeChar, "//giftcode_generate 2 5 30 - Generate 5 prime point codes (30 days expiry)");
        BuilderUtil.sendSysMessage(activeChar, "");
        BuilderUtil.sendSysMessage(activeChar, "MANAGEMENT:");
        BuilderUtil.sendSysMessage(activeChar, "//giftcode_list 50 - List last 50 codes");
        BuilderUtil.sendSysMessage(activeChar, "//giftcode_export - Export all unused codes to file");
        BuilderUtil.sendSysMessage(activeChar, "//giftcode_stats - Show usage statistics");
        BuilderUtil.sendSysMessage(activeChar, "//giftcode_delete CODE123 - Delete unused code");
        BuilderUtil.sendSysMessage(activeChar, "");
        BuilderUtil.sendSysMessage(activeChar, "REWARD TYPES:");
        BuilderUtil.sendSysMessage(activeChar, "Type 1: Premium 7 days");
        BuilderUtil.sendSysMessage(activeChar, "Type 2: 1000 Prime Points");
        BuilderUtil.sendSysMessage(activeChar, "");
        BuilderUtil.sendSysMessage(activeChar, "SECURITY FEATURES:");
        BuilderUtil.sendSysMessage(activeChar, "- One giftcode per account limit");
        BuilderUtil.sendSysMessage(activeChar, "- Rate limiting (5 attempts per hour)");
        BuilderUtil.sendSysMessage(activeChar, "- IP and HWID tracking");
        BuilderUtil.sendSysMessage(activeChar, "- Complete audit logging");
    }

    @Override
    public String[] getAdminCommandList() {
        return ADMIN_COMMANDS;
    }
}
