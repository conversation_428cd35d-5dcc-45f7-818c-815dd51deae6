package handlers.communityboard;

import java.security.MessageDigest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
// import javax.mail.*;
// import javax.mail.internet.InternetAddress;
// import javax.mail.internet.MimeMessage;
// import java.util.Properties;

import club.projectessence.Config;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.GameTimeController;
import club.projectessence.gameserver.cache.HtmCache;
import club.projectessence.gameserver.communitybbs.SunriseBoards.dropCalc.DropInfoBBSManager;
import club.projectessence.gameserver.communitybbs.SunriseBoards.dropCalc.DropInfoFunctions;
import club.projectessence.gameserver.data.ItemTable;
import club.projectessence.gameserver.data.xml.ClassListData;
import club.projectessence.gameserver.data.xml.PremiumData;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.data.xml.SkillTreeData;
import club.projectessence.gameserver.enums.ClassId;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.enums.Position;
import club.projectessence.gameserver.enums.StatModifierType;
import club.projectessence.gameserver.geoengine.GeoEngine;
import club.projectessence.gameserver.handler.CommunityBoardHandler;
import club.projectessence.gameserver.handler.IParseBoardHandler;
import club.projectessence.gameserver.instancemanager.ArtifactManager;
import club.projectessence.gameserver.instancemanager.DropManager;
import club.projectessence.gameserver.instancemanager.FactionWarManager;
import club.projectessence.gameserver.instancemanager.FactionZoneManager;
import club.projectessence.gameserver.instancemanager.PremiumManager;
import club.projectessence.gameserver.instancemanager.RandomCraftPremiumManager;
import club.projectessence.gameserver.instancemanager.VipSystemManager;
import club.projectessence.gameserver.instancemanager.ZoneManager;
import club.projectessence.gameserver.instancemanager.ZoneDominanceManager;
import club.projectessence.gameserver.model.ArtifactEntity;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.Party;
import club.projectessence.gameserver.model.SkillLearn;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.FactionWarSkillHolder;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.items.type.WeaponType;
import club.projectessence.gameserver.model.olympiad.OlympiadManager;
import club.projectessence.gameserver.model.premium.PremiumAccount;
import club.projectessence.gameserver.model.premium.PremiumGift;
import club.projectessence.gameserver.model.skills.CommonSkill;
import club.projectessence.gameserver.model.skills.FactionWarSkill;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.stats.BaseStat;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.model.stats.TraitType;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.model.zone.type.FactionZone;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.ExPremiumManagerShowHtml;
import club.projectessence.gameserver.network.serverpackets.ExSendUIEventCustom;
import club.projectessence.gameserver.network.serverpackets.ExShowScreenMessage;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;
import club.projectessence.gameserver.network.serverpackets.ShowBoard;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.network.serverpackets.commission.ExShowCommission;
import club.projectessence.gameserver.util.Util;
import club.projectessence.gameserver.discord.DiscordBotManager;
import custom.gve.service.FactionBalanceService;
import gabriel.eventEngine.interf.GabrielEvents;
import club.projectessence.gameserver.features.museum.MuseumManager;

/**
 * <AUTHOR>
 */
public class MainCBH implements IParseBoardHandler {
	private static final Logger LOGGER = Logger.getLogger(MainCBH.class.getName());
	private static int LCOIN = 91663;
	private static final String[] COMMANDS =
			{
					"_bbsHome",
					"_bbsMoneyStore",
					"_bbstop;info",
					"_bbstop;donate",
					"_bbspremium1day",
					"_bbspremium7day",
					"_bbspremium14day",
					"_bbspremium30day",
					"_bbsLcoinCheckDaily",
					"_bbstop_settings_menu",
					"_bbstop_settings_optimize",
					"sayha",
					"sayhaparty",
					"sayhacc",
					"premiumrc",
					"rcpremium",
					"premium",
					"premiumparty",
					"premiumcc",
					"_bbsstats_special",
					"_bbsstats_combat",
					"_bbsstats_utility",
					"_bbsstats_debuff",
					"hideskillsanim",
					"_bbsfactiongve",
					"_bbsfactiongve_info",
					"factiongve_skill_list",
					"factiongve_skill_view",
					"factiongve_learn_skill",
					"faction_change_personal",
					"faction_change_clan",
					"_bbsartifacts",
					"artifact_teleport",
					"_bbschangepass",
					"_bbschangepass_action",
					"_bbssetsecret_action",
					"_bbschangesecret",
					"_bbssuccess_secret",
					"_bbschangesecret_action",
					"_bbsconfirmsecret_action",
					"_bbsforgot_secret_code",
					"_bbssearchdropCalc",
					"_bbssearchdropItemsByName_",
					"_bbssearchdropMonstersByItem_",
					"_bbssearchdropMonsterDetailsByItem_",
					"_bbssearchdropMonstersByName_",
					"_bbssearchdropMonsterDetailsByName_",
					"_bbssearchNpcDropList",
					"_bbssearchShowSkills",
					"_bbsstat",
					"_bbstop_settings_player_block_down_504",
					"_bbspremium",
					"_bbspremiuminfo",
					"_bbspremiumbuy",
					"_bbspremiumgiftinfo",
					"_bbstoggle_share_premium",
					"_bbsupdate_share_rate",
					"_checksharepremium",
					"_bbstoggle_auto_resurrection",
					"_bbsgveskill",
					"_bbsgveskilllearn_",
					"_bbsgve_skill_points_info",
					"_bbsgveskill_1",
					"_bbsgveskill_2",
					"_bbsgveskill_3",
					"_bbsgveskill_4",
					"_bbsgiftcode",
					"_bbsgiftcode_use"
			};

	@Override
	public String[] getCommunityBoardCommands() {
		return COMMANDS;
	}

	@Override
	public boolean parseCommunityBoardCommand(String command, PlayerInstance player) {
		String returnHtml = null;
		String[] parts = command.split(" ");
		String baseCommand = parts[0].toLowerCase();
		// Combat check for password-related commands
		boolean isPasswordCommand = baseCommand.startsWith("_bbschangepass") || baseCommand.startsWith("_bbssetsecret_action") || baseCommand.startsWith("_bbschangesecret") || baseCommand.startsWith("_bbssuccess_secret") || baseCommand.startsWith("_bbsconfirmsecret_action");
		if (isPasswordCommand && (player.isCastingNow() || player.isInCombat() || player.isInDuel() || player.isInOlympiadMode() || player.isInsideZone(ZoneId.SIEGE) || player.isInsideZone(ZoneId.PVP) || (player.getPvpFlag() > 0) || player.isAlikeDead() || player.isOnEvent())) {
			player.sendMessage("You can't use the Community Board right now.");
			return false;
		}
		switch (baseCommand) {
			case "_bbshome": {
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/home.html");
				returnHtml = replaceVars(player, returnHtml);
				CommunityBoardHandler.separateAndSend(returnHtml, player);
				break;
			}
			case "_bbstop;info": {
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/info.html");
				returnHtml = replaceVars(player, returnHtml);
				CommunityBoardHandler.separateAndSend(returnHtml, player);
				break;
			}
			case "_bbstop;donate": {
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/donate.html");
				returnHtml = replaceVars(player, returnHtml);
				CommunityBoardHandler.separateAndSend(returnHtml, player);
				break;
			}
			case "_bbsmoneystore": {
				player.sendPacket(ExShowCommission.STATIC_PACKET);
				player.sendPacket(new ExSendUIEventCustom(ExSendUIEventCustom.MONEY_STORE_BALANCE, (player.getPrimePoints() * 100)));
				player.sendPacket(new ShowBoard());
				break;
			}
			// case "_bbspremium1day":
			// {
			// final int premiumDays = 1;
			// final int price = 20;
			// if (player.getPrimePoints() < price)
			// {
			// player.sendMessage("Not enough currency!");
			// }
			// else
			// {
			// player.setPrimePoints(player.getPrimePoints() - price);
			// PremiumManager.getInstance().addPremiumTime(player.getAccountName(), premiumDays, TimeUnit.DAYS);
			// player.sendMessage("Your account will now have premium status until " + new SimpleDateFormat("dd.MM.yyyy HH:mm").format(PremiumManager.getInstance().getPremiumExpiration(player.getAccountName())) + ".");
			// }
			// break;
			// }
			// case "_bbspremium7day":
			// {
			// final int premiumDays = 7;
			// final int price = 60;
			// if (player.getPrimePoints() < price)
			// {
			// player.sendMessage("Not enough currency!");
			// }
			// else
			// {
			// player.setPrimePoints(player.getPrimePoints() - price);
			// PremiumManager.getInstance().addPremiumTime(player.getAccountName(), premiumDays, TimeUnit.DAYS);
			// player.sendMessage("Your account will now have premium status until " + new SimpleDateFormat("dd.MM.yyyy HH:mm").format(PremiumManager.getInstance().getPremiumExpiration(player.getAccountName())) + ".");
			// }
			// break;
			// }
			// case "_bbspremium14day":
			// {
			// final int premiumDays = 14;
			// final int price = 100;
			// if (player.getPrimePoints() < price)
			// {
			// player.sendMessage("Not enough currency!");
			// }
			// else
			// {
			// player.setPrimePoints(player.getPrimePoints() - price);
			// PremiumManager.getInstance().addPremiumTime(player.getAccountName(), premiumDays, TimeUnit.DAYS);
			// player.sendMessage("Your account will now have premium status until " + new SimpleDateFormat("dd.MM.yyyy HH:mm").format(PremiumManager.getInstance().getPremiumExpiration(player.getAccountName())) + ".");
			// }
			// break;
			// }
			// case "_bbspremium30day":
			// {
			// final int premiumDays = 30;
			// final int price = 150;
			// if (player.getPrimePoints() < price)
			// {
			// player.sendMessage("Not enough currency!");
			// }
			// else
			// {
			// player.setPrimePoints(player.getPrimePoints() - price);
			// PremiumManager.getInstance().addPremiumTime(player.getAccountName(), premiumDays, TimeUnit.DAYS);
			// player.sendMessage("Your account will now have premium status until " + new SimpleDateFormat("dd.MM.yyyy HH:mm").format(PremiumManager.getInstance().getPremiumExpiration(player.getAccountName())) + ".");
			// }
			// break;
			// }
			case "_bbstop_settings_menu": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/menu/player.htm");
				dialog = replaceVars(player, dialog);
				player.sendPacket(new NpcHtmlMessage(dialog));
				break;
			}
			case "_bbstop_settings_optimize": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/menu/optimize.htm");
				dialog = replaceVars(player, dialog);
				player.sendPacket(new NpcHtmlMessage(dialog));
				break;
			}
			case "sayha": {
				if (player.hasPremiumStatus()) {
					long expiration = PremiumManager.getInstance().getPremiumExpiration(player.getObjectId());
					if (Config.ALL_PREMIUM_EVENT && (expiration < System.currentTimeMillis())) {
						player.sendPacket(new ExShowScreenMessage("Premium account event is currently active", 2, 5000, 0, true, true));
					} else {
						Calendar c = Calendar.getInstance();
						c.setTimeInMillis(expiration);
						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						String txt = "Premium expiration date: " + sdf.format(c.getTime());
						player.sendPacket(new ExShowScreenMessage(txt, 2, 5000, 0, true, true));
					}
				} else {
					player.sendPacket(new ExShowScreenMessage("Your account isn't premium", 2, 5000, 0, true, true));
				}
				break;
			}
			case "sayhaparty": {
				List<PlayerInstance> players = null;
				if (player.getParty() == null) {
					player.sendMessage("Not in party.");
					return true;
				}
				players = player.getParty().getMembers();
				if (players == null) {
					player.sendMessage("Usage: ." + command + " <party>");
					return true;
				}
				boolean found = false;
				for (PlayerInstance memb : players) {
					if (memb.getSayhaGracePoints() <= 0) {
						player.sendMessage(memb.getName() + " is without sayha.");
						found = true;
					}
				}
				if (!found) {
					player.sendMessage("Everyone has sayha.");
				}
				break;
			}
			case "sayhacc": {
				List<PlayerInstance> players = null;
				if (player.getCommandChannel() == null) {
					player.sendMessage("Not in command channel.");
					return true;
				}
				players = player.getCommandChannel().getMembers();
				if (players == null) {
					player.sendMessage("Usage: ." + command + " <cc>");
					return true;
				}
				boolean found = false;
				for (PlayerInstance memb : players) {
					if (memb.getSayhaGracePoints() <= 0) {
						player.sendMessage(memb.getName() + " is without sayha.");
						found = true;
					}
				}
				if (!found) {
					player.sendMessage("Everyone has sayha.");
				}
				break;
			}
			case "premiumrc":
			case "rcpremium": {
				if (player.hasRandomCraftPremiumStatus()) {
					long expiration = RandomCraftPremiumManager.getInstance().getPremiumExpiration(player.getAccountName());
					if (Config.ALL_RANDOM_CRAFT_PREMIUM_EVENT && (expiration < System.currentTimeMillis())) {
						player.sendPacket(new ExShowScreenMessage("Premium Random Craft event is currently active", 2, 5000, 0, true, true));
					} else {
						Calendar c = Calendar.getInstance();
						c.setTimeInMillis(expiration);
						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						String txt = "Random Craft Premium expiration date: " + sdf.format(c.getTime());
						player.sendPacket(new ExShowScreenMessage(txt, 2, 5000, 0, true, true));
					}
				} else {
					player.sendPacket(new ExShowScreenMessage("Your account doesn't have premium random craft.", 2, 5000, 0, true, true));
				}
				break;
			}
			case "premium": {
				if (player.hasPremiumStatus()) {
					long expiration = PremiumManager.getInstance().getPremiumExpiration(player.getObjectId());
					if (Config.ALL_PREMIUM_EVENT && (expiration < System.currentTimeMillis())) {
						player.sendPacket(new ExShowScreenMessage("Premium account event is currently active", 2, 5000, 0, true, true));
					} else {
						Calendar c = Calendar.getInstance();
						c.setTimeInMillis(expiration);
						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						String txt = "Premium expiration date: " + sdf.format(c.getTime());
						player.sendPacket(new ExShowScreenMessage(txt, 2, 5000, 0, true, true));
					}
				} else {
					player.sendPacket(new ExShowScreenMessage("Your account isn't premium", 2, 5000, 0, true, true));
				}
				break;
			}
			case "premiumparty": {
				List<PlayerInstance> players = null;
				if (player.getParty() == null) {
					player.sendMessage("Not in party.");
					return true;
				}
				players = player.getParty().getMembers();
				if (players == null) {
					player.sendMessage("Usage: ." + command + " <party>");
					return true;
				}
				boolean found = false;
				for (PlayerInstance memb : players) {
					if (!memb.hasPremiumStatus()) {
						player.sendMessage(memb.getName() + " is without premium account.");
						found = true;
					}
				}
				if (!found) {
					player.sendMessage("Everyone has premium account.");
				}
				break;
			}
			case "premiumcc": {
				List<PlayerInstance> players = null;
				if (player.getCommandChannel() == null) {
					player.sendMessage("Not in command channel.");
					return true;
				}
				players = player.getCommandChannel().getMembers();
				if (players == null) {
					player.sendMessage("Usage: ." + command + " <cc>");
					return true;
				}
				boolean found = false;
				for (PlayerInstance memb : players) {
					if (!memb.hasPremiumStatus()) {
						player.sendMessage(memb.getName() + " is without premium account.");
						found = true;
					}
				}
				if (!found) {
					player.sendMessage("Everyone has premium account.");
				}
				break;
			}
			case "_bbsstats_combat": {
				NumberFormat df = NumberFormat.getInstance(Locale.ENGLISH);
				df.setMaximumFractionDigits(1);
				df.setMinimumFractionDigits(1);
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/stats/combat.htm");
				dialog = dialog.replace("%critical_damage%", df.format(player.getCriticalDmg((int) 1.66)) + "x +" + player.getStat().getValue(Stat.CRITICAL_DAMAGE_ADD, 0));
				dialog = dialog.replace("%Mcritical_damage%", String.valueOf(player.getStat().getValue(Stat.MAGIC_CRITICAL_DAMAGE, 1)) + "x +" + player.getStat().getValue(Stat.MAGIC_CRITICAL_DAMAGE_ADD, 0));
				dialog = dialog.replace("%fatal_blow%", String.valueOf((int) player.getStat().getValue(Stat.BLOW_RATE)));
				dialog = dialog.replace("%fatal_blow_side%", String.valueOf(player.getStat().getPositionTypeValue(Stat.CRITICAL_RATE, Position.SIDE)));
				dialog = dialog.replace("%fatal_blow_back%", String.valueOf(player.getStat().getPositionTypeValue(Stat.CRITICAL_RATE, Position.BACK)));
				ItemInstance shld = player.getSecondaryWeaponInstance();
				boolean shield = (shld != null) && (shld.getItemType() == WeaponType.NONE);
				dialog = dialog.replace("%shield_def%", shield ? String.valueOf(player.getStat().getValue(Stat.SHIELD_DEFENCE, player.getTemplate().getBaseShldDef())) : "0");
				int shldRate = (int) (player.getStat().getValue(Stat.SHIELD_DEFENCE_RATE) * BaseStat.CON.calcBonus(player));
				dialog = dialog.replace("%shield_rate%", shldRate + "%");
				dialog = dialog.replace("%pvp_damage_def%", String.valueOf((int) player.getStat().getValue(Stat.PVP_PHYSICAL_ATTACK_DAMAGE, 0) + "% / " + (int) player.getStat().getValue(Stat.PVP_PHYSICAL_ATTACK_DEFENCE, 0)));
				dialog = dialog.replace("%pvp_magic_damage_def%", String.valueOf((int) player.getStat().getValue(Stat.PVP_MAGICAL_SKILL_DAMAGE, 0) + "% / " + (int) player.getStat().getValue(Stat.PVP_MAGICAL_SKILL_DEFENCE, 0)));
				dialog = dialog.replace("%pvp_skill_damage_def%", String.valueOf((int) player.getStat().getValue(Stat.PVP_PHYSICAL_SKILL_DAMAGE, 0) + "% / " + (int) player.getStat().getValue(Stat.PVP_PHYSICAL_SKILL_DEFENCE, 0)));
				dialog = dialog.replace("%critical_def%", String.valueOf((int) player.getStat().getValue(Stat.DEFENCE_CRITICAL_RATE, 0) + "%"));
				dialog = dialog.replace("%patk_range_count%", String.valueOf(player.getPhysicalAttackRange() + " / " + (int) (player.getStat().getValue(Stat.ATTACK_COUNT_MAX, 1))));
				dialog = dialog.replace("%skill_power%", String.valueOf((int) player.getStat().getValue(Stat.PHYSICAL_SKILL_POWER, 0) + "% / " + (int) (player.getStat().getValue(Stat.MAGICAL_SKILL_POWER, 0)) + "%"));
				player.sendPacket(new ExPremiumManagerShowHtml(dialog));
				break;
			}
			case "_bbsstats_utility": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/stats/utility.htm");
				dialog = dialog.replace("%reflect_damage%", String.valueOf((int) player.getStat().getValue(Stat.REFLECT_DAMAGE_PERCENT, 0)) + "%");
				dialog = dialog.replace("%reflect_def%", String.valueOf((int) player.getStat().getValue(Stat.REFLECT_DAMAGE_PERCENT_DEFENSE, 0)) + "%");
				dialog = dialog.replace("%mana_vamp%", String.valueOf((int) player.getStat().getValue(Stat.ABSORB_MANA_DAMAGE_PERCENT, 0)) + "%");
				dialog = dialog.replace("%buff_limit%", String.valueOf(player.getStat().getMaxBuffCount()));
				dialog = dialog.replace("%physical_reuse%", String.valueOf(player.getStat().getReuseTypeValue(0)) + "%");
				dialog = dialog.replace("%magical_reuse%", String.valueOf(player.getStat().getReuseTypeValue(1)) + "%");
				dialog = dialog.replace("%physical_skill_cost%", String.valueOf(player.getStat().getMpConsumeTypeValue(0)) + "%");
				dialog = dialog.replace("%magical_skill_cost%", String.valueOf(player.getStat().getMpConsumeTypeValue(1)) + "%");
				dialog = dialog.replace("%hp_mp_cp_regen%", String.valueOf(player.getStat().getHpRegen() + " / " + player.getStat().getMpRegen() + " / " + player.getStat().getCpRegen()));
				dialog = dialog.replace("%healing_received%", String.valueOf((int) player.getStat().getValue(Stat.HEAL_EFFECT_ADD, 100)) + "%");
				dialog = dialog.replace("%mana_received%", String.valueOf((int) player.getStat().getValue(Stat.MANA_CHARGE, 100)) + "%");
				player.sendPacket(new ExPremiumManagerShowHtml(dialog));
				break;
			}
			case "_bbsstats_debuff": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/stats/debuff.htm");
				dialog = dialog.replace("%paralyze_vul%", player.getStat().hasDefenceTrait(TraitType.PARALYZE) ? String.valueOf((int) (player.getStat().getDefenceTrait(TraitType.PARALYZE) * 100) + "%") : "0%");
				dialog = dialog.replace("%stun_vul%", player.getStat().hasDefenceTrait(TraitType.SHOCK) ? String.valueOf((int) (player.getStat().getDefenceTrait(TraitType.SHOCK) * 100) + "%") : "0%");
				dialog = dialog.replace("%sleep_vul%", player.getStat().hasDefenceTrait(TraitType.SLEEP) ? String.valueOf((int) (player.getStat().getDefenceTrait(TraitType.SLEEP) * 100) + "%") : "0%");
				dialog = dialog.replace("%imprison_vul%", player.getStat().hasDefenceTrait(TraitType.IMPRISONMENT) ? String.valueOf((int) (player.getStat().getDefenceTrait(TraitType.IMPRISONMENT) * 100) + "%") : "0%");
				dialog = dialog.replace("%pull_vul%", player.getStat().hasDefenceTrait(TraitType.PULL) ? String.valueOf((int) (player.getStat().getDefenceTrait(TraitType.PULL) * 100) + "%") : "0%");
				dialog = dialog.replace("%fear_vul%", player.getStat().hasDefenceTrait(TraitType.DERANGEMENT) ? String.valueOf((int) (player.getStat().getDefenceTrait(TraitType.DERANGEMENT) * 100) + "%") : "0%");
				dialog = dialog.replace("%silence_vul%", player.getStat().hasDefenceTrait(TraitType.DERANGEMENT) ? String.valueOf((int) (player.getStat().getDefenceTrait(TraitType.DERANGEMENT) * 100) + "%") : "0%");
				dialog = dialog.replace("%hold_vul%", player.getStat().hasDefenceTrait(TraitType.HOLD) ? String.valueOf((int) (player.getStat().getDefenceTrait(TraitType.HOLD) * 100) + "%") : "0%");
				dialog = dialog.replace("%suppression_vul%", player.getStat().hasDefenceTrait(TraitType.SUPPRESSION) ? String.valueOf((int) (player.getStat().getDefenceTrait(TraitType.SUPPRESSION) * 100) + "%") : "0%");
				dialog = dialog.replace("%infection_vul%", player.getStat().hasDefenceTrait(TraitType.POISON) ? String.valueOf((int) (player.getStat().getDefenceTrait(TraitType.POISON) * 100) + "%") : "0%");
				double anomalyResist = (player.getStat().getValue(Stat.RESIST_ABNORMAL_DEBUFF, 1) * 100);
				double finalAnomalyResist = 100 - anomalyResist;
				dialog = dialog.replace("%debuff_vul%", String.valueOf((int) finalAnomalyResist + "%"));
				dialog = dialog.replace("%paralyze_power%", player.getStat().hasAttackTrait(TraitType.PARALYZE) ? String.valueOf((int) ((player.getStat().getAttackTrait(TraitType.PARALYZE) - 1) * 100) + "%") : "0%");
				dialog = dialog.replace("%stun_power%", player.getStat().hasAttackTrait(TraitType.SHOCK) ? String.valueOf((int) ((player.getStat().getAttackTrait(TraitType.SHOCK) - 1) * 100) + "%") : "0%");
				dialog = dialog.replace("%sleep_power%", player.getStat().hasAttackTrait(TraitType.SLEEP) ? String.valueOf((int) ((player.getStat().getAttackTrait(TraitType.SLEEP) - 1) * 100) + "%") : "0%");
				dialog = dialog.replace("%imprison_power%", player.getStat().hasAttackTrait(TraitType.IMPRISONMENT) ? String.valueOf((int) ((player.getStat().getAttackTrait(TraitType.IMPRISONMENT) - 1) * 100) + "%") : "0%");
				dialog = dialog.replace("%pull_power%", player.getStat().hasAttackTrait(TraitType.PULL) ? String.valueOf((int) ((player.getStat().getAttackTrait(TraitType.PULL) - 1) * 100) + "%") : "0%");
				dialog = dialog.replace("%fear_power%", player.getStat().hasAttackTrait(TraitType.DERANGEMENT) ? String.valueOf((int) ((player.getStat().getAttackTrait(TraitType.DERANGEMENT) - 1) * 100) + "%") : "0%");
				dialog = dialog.replace("%silence_power%", player.getStat().hasAttackTrait(TraitType.DERANGEMENT) ? String.valueOf((int) ((player.getStat().getAttackTrait(TraitType.DERANGEMENT) - 1) * 100) + "%") : "0%");
				dialog = dialog.replace("%hold_power%", player.getStat().hasAttackTrait(TraitType.HOLD) ? String.valueOf((int) ((player.getStat().getAttackTrait(TraitType.HOLD) - 1) * 100) + "%") : "0%");
				dialog = dialog.replace("%suppression_power%", player.getStat().hasDefenceTrait(TraitType.SUPPRESSION) ? String.valueOf((int) ((player.getStat().getAttackTrait(TraitType.SUPPRESSION) - 1) * 100) + "%") : "0%");
				dialog = dialog.replace("%infection_power%", player.getStat().hasAttackTrait(TraitType.POISON) ? String.valueOf((int) ((player.getStat().getAttackTrait(TraitType.POISON) - 1) * 100) + "%") : "0%");
				double anomalyPower = (player.getStat().getValue(Stat.ABNORMAL_DEBUFF_RATE, 1) * 100);
				double finalAnomalyPower = 100 - anomalyPower;
				dialog = dialog.replace("%debuff_power%", String.valueOf((int) finalAnomalyPower + "%"));
				player.sendPacket(new ExPremiumManagerShowHtml(dialog));
				break;
			}
			case "hideskillsanim": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/menu/optimize.htm");
				dialog = replaceVars(player, dialog);
				final boolean oldValue = player.getVariables().getBoolean("HIDE_SKILL_ANIMATION", false);
				player.getVariables().set("HIDE_SKILL_ANIMATION", !oldValue);
				player.sendMessage("Your skill animation will be " + (oldValue ? "Disabled" : "Enabled"));
				player.sendPacket(new NpcHtmlMessage(dialog));
				break;
			}

			case "_bbsfactiongve": {
				FactionZoneManager zoneManager = FactionZoneManager.getInstance();
				List<FactionZone> zones = new ArrayList<>(ZoneManager.getInstance().getAllZones(FactionZone.class));
				int totalZones = zones.size();
				int waterZones = 0;
				int fireZones = 0;
				int waterScore = FactionWarManager.getInstance().getFactionPoints(Faction.WATER);
				int fireScore = FactionWarManager.getInstance().getFactionPoints(Faction.FIRE);
				for (FactionZone zone : zones) {
					if (zone.getControllingFaction() == Faction.WATER) {
						waterZones++;
					} else if (zone.getControllingFaction() == Faction.FIRE) {
						fireZones++;
					}
				}
				double waterPercent = FactionBalanceService.getInstance().getFactionPlayerPercentage(Faction.WATER);
				double firePercent = FactionBalanceService.getInstance().getFactionPlayerPercentage(Faction.FIRE);
				double waterControlPercent = ZoneDominanceManager.getInstance().getFactionZoneControlPercentage(Faction.WATER);
				double fireControlPercent = ZoneDominanceManager.getInstance().getFactionZoneControlPercentage(Faction.FIRE);
				// LOGGER.info("CBH Faction percentages - FIRE: " + firePercent + "%, WATER: " + waterPercent + ", Fire Control%: " + fireControlPercent + "%, Water Control%: " + waterControlPercent);
				// Force update faction counts to ensure latest data
				FactionBalanceService.getInstance().updateFactionCounts();
				waterPercent = FactionBalanceService.getInstance().getFactionPlayerPercentage(Faction.WATER);
				firePercent = FactionBalanceService.getInstance().getFactionPlayerPercentage(Faction.FIRE);
				// LOGGER.info("CBH Updated Faction percentages - FIRE: " + firePercent + "%, WATER: " + waterPercent);
				// Handle pagination for Faction Zones
				int zonesPerPage = 5; // Number of zones per page
				int totalZonePages = totalZones > 0 ? (int) Math.ceil((double) totalZones / zonesPerPage) : 1;
				int zonePage = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
				zonePage = Math.max(0, Math.min(zonePage, totalZonePages - 1)); // Ensure zonePage is within valid range
				// Handle pagination for Artifacts
				int artifactsPerPage = 3; // Number of artifacts per page
				List<ArtifactEntity> artifacts = new ArrayList<>(ArtifactManager.getInstance().getArtifacts());
				int totalArtifacts = artifacts.size();
				int totalArtifactPages = totalArtifacts > 0 ? (int) Math.ceil((double) totalArtifacts / artifactsPerPage) : 1;
				int artifactPage = parts.length > 2 ? Integer.parseInt(parts[2]) : 0;
				artifactPage = Math.max(0, Math.min(artifactPage, totalArtifactPages - 1)); // Ensure artifactPage is within valid range
				// Generate HTML for Faction Zones
				StringBuilder zoneListHtml = new StringBuilder();
				int startIndex = zonePage * zonesPerPage;
				int endIndex = Math.min(startIndex + zonesPerPage, totalZones);
				for (int i = startIndex; i < endIndex; i++) {
					FactionZone zone = zones.get(i);
					zoneListHtml.append("<tr>");
					zoneListHtml.append("<td align=\"left\" width=\"320\" height=\"25\">");
					zoneListHtml.append("<table width=\"320\" cellpadding=\"0\" cellspacing=\"0\"><tr>");
					zoneListHtml.append("<td width=\"250\" align=\"left\">");
					zoneListHtml.append("<font name=\"hs09\" color=\"FFFFFF\">").append(zone.getInGameName()).append("</font>");
					Faction faction = zone.getControllingFaction();
					if (faction == Faction.FIRE) {
						zoneListHtml.append(" <font color=\"FF0000\">(Owner by Fire)</font>");
					} else if (faction == Faction.WATER) {
						zoneListHtml.append(" <font color=\"48BEFA\">(Owner by Water)</font>");
					} else {
						zoneListHtml.append(" <font color=\"FFFFFF\">(Neutral)</font>");
					}
					zoneListHtml.append("</td>");
					zoneListHtml.append("<td width=\"70\" align=\"right\">");
					if (faction == Faction.FIRE && fireZones > 0 && player.getFaction() == Faction.FIRE) {
						zoneListHtml.append("<button value=\"Teleport\" action=\"bypass gve_teleport_unrestricted ").append(zone.getName()).append("\" width=\"60\" height=\"20\" back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">");
					} else if (faction == Faction.WATER && waterZones > 0 && player.getFaction() == Faction.WATER) {
						zoneListHtml.append("<button value=\"Teleport\" action=\"bypass gve_teleport_unrestricted ").append(zone.getName()).append("\" width=\"60\" height=\"20\" back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">");
					}
					zoneListHtml.append("</td>");
					zoneListHtml.append("</tr></table>");
					zoneListHtml.append("</td>");
					zoneListHtml.append("</tr>");
				}
				// Generate HTML for Active Zones
				StringBuilder activeZonesHtml = new StringBuilder();
				for (FactionZone zone : zones) {
					FactionZoneManager.ZoneStatus status = zoneManager.getZoneStatus(zone.getName());
					if (status == FactionZoneManager.ZoneStatus.ACTIVATED) {
						activeZonesHtml.append("<tr>");
						activeZonesHtml.append("<td align=\"left\" width=\"320\" height=\"30\">");
						activeZonesHtml.append("<table width=\"320\" cellpadding=\"2\" cellspacing=\"0\"><tr>");
						activeZonesHtml.append("<td width=\"230\" align=\"left\">");
						int currentPlayers = zone.getPlayersInZone().size();
						int maxPlayers = Config.BALANCE_LOCATIONS.getOrDefault(zone.getName(), Config.MAX_PLAYERS_PER_ZONE);
						String playerCountDisplay = currentPlayers + "/" + maxPlayers;
						String color = (currentPlayers >= maxPlayers * 0.8) ? "FF0000" : "FFFFFF"; // Đỏ khi > 80%, trắng khi còn chỗ
						activeZonesHtml.append("<font name=\"hs09\" color=\"" + color + "\">").append(zone.getInGameName()).append(" (").append(playerCountDisplay).append(")</font>");
						if (zone.isUnderAttack()) {
							activeZonesHtml.append(" <font color=\"FF0000\">(Under Attack)</font>");
						}
						activeZonesHtml.append(" ");
						if (zone.isUnderProtection()) {
							long protectionEndTime = zone.getProtectionEndTime();
							long currentTime = System.currentTimeMillis();
							long remainingTime = protectionEndTime - currentTime;
							if (remainingTime > 0) {
								long minutes = TimeUnit.MILLISECONDS.toMinutes(remainingTime);
								long seconds = TimeUnit.MILLISECONDS.toSeconds(remainingTime) - TimeUnit.MINUTES.toSeconds(minutes);
								activeZonesHtml.append("<font color=\"FFFF00\">Protected: ").append(minutes).append("m ").append(seconds).append("s</font>");
							} else {
								activeZonesHtml.append("<font color=\"00FF00\">No protection</font>");
							}
						} else {
							activeZonesHtml.append("<font color=\"00FF00\">No protection</font>");
						}
						if (Config.FACTION_BALANCE_IN_LOCATIONS) {
							long fireCount = zone.getPlayersInZone().stream().filter(p -> p.getFaction() == Faction.FIRE).count();
							long waterCount = zone.getPlayersInZone().stream().filter(p -> p.getFaction() == Faction.WATER).count();
							long totalFactionPlayers = fireCount + waterCount;
							if (totalFactionPlayers > 0) {
								double zoneFirePercent = (fireCount * 100.0) / totalFactionPlayers;
								double zoneWaterPercent = (waterCount * 100.0) / totalFactionPlayers;
								activeZonesHtml.append(" <font color=\"FFFF00\">(<font color=\"FF0000\">Fire: ").append(Math.round(zoneFirePercent)).append("%</font>, <font color=\"48BEFA\">Water: ").append(Math.round(zoneWaterPercent)).append("%</font>)</font>");
							}
						}
						activeZonesHtml.append("</td>");
						activeZonesHtml.append("<td width=\"90\" align=\"right\">");
						Faction controllingFaction = zone.getControllingFaction();
						boolean showTeleport = false;
						// LOGGER.info("Zone: " + zone.getName() + ", Status: " + status + ", ControllingFaction: " + (controllingFaction != null ? controllingFaction : "null") + ", PlayerFaction: " + player.getFaction() + ", UnderAttack: " + zone.isUnderAttack() + ", UnderProtection: " + zone.isUnderProtection());
						if (zone.getStatus() == FactionZoneManager.ZoneStatus.ACTIVATED) {
							if (controllingFaction == null || controllingFaction == Faction.NONE) {
								showTeleport = true;
								// LOGGER.info("Teleport condition 1 met for zone: " + zone.getName() + " (Neutral/None)");
							} else if (player.getFaction() == controllingFaction && !zone.isUnderAttack()) {
								showTeleport = true;
								// LOGGER.info("Teleport condition 2 met for zone: " + zone.getName() + " (Own faction, not under attack)");
							}
							// Thêm điều kiện mới: Cho phép teleport khi hết thời gian bảo vệ, bất kể đang bị tấn công
							else if (!zone.isUnderProtection()) {
								showTeleport = true;
								// LOGGER.info("Teleport condition 3 met for zone: " + zone.getName() + " (No protection, can reclaim)");
							}
						}
						if (!showTeleport) {
							LOGGER.info("Teleport not shown for zone: " + zone.getName() + " due to conditions.");
						}
						if (showTeleport) {
							activeZonesHtml.append("<button value=\"Teleport\" action=\"bypass factiongve_teleport ").append(zone.getName()).append("\" width=\"80\" height=\"20\" back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">");
						}
						activeZonesHtml.append("</td>");
						activeZonesHtml.append("</tr></table>");
						activeZonesHtml.append("</td>");
						activeZonesHtml.append("</tr>");
					}
				}
				if (activeZonesHtml.length() == 0) {
					activeZonesHtml.append("<tr>");
					activeZonesHtml.append("<td align=\"left\" width=\"320\" height=\"30\">");
					activeZonesHtml.append("<font color=\"FFFFFF\">No active zones</font>");
					activeZonesHtml.append("</td>");
					activeZonesHtml.append("</tr>");
				}
				// Generate HTML for Artifacts with pagination
				StringBuilder artifactsHtml = new StringBuilder();
				int startArtifactIndex = artifactPage * artifactsPerPage;
				int endArtifactIndex = Math.min(startArtifactIndex + artifactsPerPage, totalArtifacts);
				if (artifacts.isEmpty()) {
					artifactsHtml.append("<tr>");
					artifactsHtml.append("<td align=\"left\" width=\"320\" height=\"30\">");
					artifactsHtml.append("<font color=\"FFFFFF\">No artifacts available</font>");
					artifactsHtml.append("</td>");
					artifactsHtml.append("</tr>");
				} else {
					for (int i = startArtifactIndex; i < endArtifactIndex; i++) {
						ArtifactEntity artifact = artifacts.get(i);
						if (artifact.getArtifact() == null)
							continue; // Skip if Artifact does not exist
						artifactsHtml.append("<tr>");
						artifactsHtml.append("<td align=\"left\" width=\"320\" height=\"25\">");
						artifactsHtml.append("<table width=\"320\" cellpadding=\"0\" cellspacing=\"0\"><tr>");
						artifactsHtml.append("<td width=\"250\" align=\"left\">");
						artifactsHtml.append("<font name=\"hs09\" color=\"FFFFFF\">").append(artifact.getName(player)).append("</font>");
						Faction faction = artifact.getFraction();
						if (faction == Faction.FIRE) {
							artifactsHtml.append(" <font color=\"FF0000\">(Owner by Fire)</font>");
						} else if (faction == Faction.WATER) {
							artifactsHtml.append(" <font color=\"48BEFA\">(Owner by Water)</font>");
						}
						artifactsHtml.append("</td>");
						artifactsHtml.append("<td width=\"70\" align=\"right\">");
						// Check conditions to display the teleport button
						boolean canTeleport = faction == player.getFaction() || faction == Faction.NONE;
						if (canTeleport) {
							artifactsHtml.append("<button value=\"Teleport\" action=\"bypass artifact_teleport ").append(artifact.getTemplate().getId()).append("\" width=\"60\" height=\"20\" back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">");
						}
						artifactsHtml.append("</td>");
						artifactsHtml.append("</tr></table>");
						artifactsHtml.append("</td>");
						artifactsHtml.append("</tr>");
					}
				}
				// Generate HTML for Previous and Next buttons for Faction Zones
				String prevPageHtml = zonePage > 0 ? "<button value=\"Previous\" action=\"bypass _bbsfactiongve " + (zonePage - 1) + " " + artifactPage + "\" width=\"60\" height=\"20\" back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">" : "";
				String nextPageHtml = zonePage < totalZonePages - 1 ? "<button value=\"Next\" action=\"bypass _bbsfactiongve " + (zonePage + 1) + " " + artifactPage + "\" width=\"60\" height=\"20\" back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">" : "";
				// Generate HTML for Previous and Next buttons for Artifacts
				String prevArtifactPageHtml = artifactPage > 0 ? "<button value=\"Previous\" action=\"bypass _bbsfactiongve " + zonePage + " " + (artifactPage - 1) + "\" width=\"60\" height=\"20\" back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">" : "";
				String nextArtifactPageHtml = artifactPage < totalArtifactPages - 1 ? "<button value=\"Next\" action=\"bypass _bbsfactiongve " + zonePage + " " + (artifactPage + 1) + "\" width=\"60\" height=\"20\" back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">" : "";
				// Retrieve top 10 players from globalRankings
				List<FactionWarManager.PlayerRanking> globalRankings = FactionWarManager.getInstance().getGlobalRankings();
				StringBuilder top10Html = new StringBuilder();
				int maxDisplay = Math.min(10, globalRankings.size());

				// Add status header
				boolean isWarActive = FactionWarManager.getInstance().isWarActive();
				String statusText = isWarActive ? "Current Session Rankings" : "Last Session Rankings";
				String statusColor = isWarActive ? "00FF00" : "LEVEL";

				top10Html.append("<tr>");
				top10Html.append("<td align=\"center\" height=\"20\">");
				top10Html.append("<font name=\"hs09\" color=\"").append(statusColor).append("\">").append(statusText).append("</font>");
				top10Html.append("</td>");
				top10Html.append("</tr>");

				if (maxDisplay == 0) {
					top10Html.append("<tr>");
					top10Html.append("<td align=\"center\" height=\"25\">");
					String noRankingsText = isWarActive ? "No players with points yet" : "No rankings available";
					top10Html.append("<font color=\"FFFFFF\">").append(noRankingsText).append("</font>");
					top10Html.append("</td>");
					top10Html.append("</tr>");
				} else {
					for (int i = 0; i < maxDisplay; i++) {
						FactionWarManager.PlayerRanking ranking = globalRankings.get(i);
						PlayerInstance rankedPlayer = ranking.getPlayer();
						String factionColor = rankedPlayer.getFaction() == Faction.FIRE ? "FA9A48" : "48BEFA";
						String factionName = rankedPlayer.getFaction() == Faction.FIRE ? "Fire" : "Water";
						top10Html.append("<tr>");
						top10Html.append("<td align=\"center\" height=\"25\">");
						top10Html.append("<table width=\"256\" cellpadding=\"0\" cellspacing=\"0\"><tr>");
						top10Html.append("<td width=\"30\" align=\"left\">");
						top10Html.append("<font name=\"hs09\" color=\"LEVEL\">#").append(i + 1).append("</font>");
						top10Html.append("</td>");
						top10Html.append("<td width=\"100\" align=\"left\">");
						top10Html.append("<font name=\"hs09\" color=\"FFFFFF\">").append(rankedPlayer.getName()).append("</font>");
						top10Html.append("</td>");
						top10Html.append("<td width=\"60\" align=\"center\">");
						top10Html.append("<font name=\"hs09\" color=\"").append(factionColor).append("\">").append(factionName).append("</font>");
						top10Html.append("</td>");
						/*top10Html.append("<td width=\"60\" align=\"right\">");
						top10Html.append("<font name=\"hs09\" color=\"LEVEL\">").append(ranking.getPoints()).append("</font>");
						top10Html.append("</td>");*/
						top10Html.append("</tr></table>");
						top10Html.append("</td>");
						top10Html.append("</tr>");
					}
				}
				// Load and replace HTML
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/gve/home.html");
				returnHtml = returnHtml.replace("%online%", String.valueOf(World.getInstance().getPlayers().size()));
				returnHtml = returnHtml.replace("%faction_zones%", zoneListHtml.toString());
				returnHtml = returnHtml.replace("%prev_page%", prevPageHtml);
				returnHtml = returnHtml.replace("%next_page%", nextPageHtml);
				returnHtml = returnHtml.replace("%active_zones%", activeZonesHtml.toString());
				returnHtml = returnHtml.replace("%artifacts%", artifactsHtml.toString());
				returnHtml = returnHtml.replace("%prev_artifact_page%", prevArtifactPageHtml);
				returnHtml = returnHtml.replace("%next_artifact_page%", nextArtifactPageHtml);
				returnHtml = returnHtml.replace("%water_percent%", String.valueOf(Math.round(waterPercent)));
				returnHtml = returnHtml.replace("%fire_percent%", String.valueOf(Math.round(firePercent)));
				returnHtml = returnHtml.replace("%water_control_percent%", String.valueOf(Math.round(waterControlPercent)));
				returnHtml = returnHtml.replace("%fire_control_percent%", String.valueOf(Math.round(fireControlPercent)));
				returnHtml = returnHtml.replace("%water_score%", String.valueOf(waterScore));
				returnHtml = returnHtml.replace("%fire_score%", String.valueOf(fireScore));
				returnHtml = returnHtml.replace("%water_points%", String.valueOf(waterScore));
				returnHtml = returnHtml.replace("%fire_points%", String.valueOf(fireScore));
				returnHtml = returnHtml.replace("%top_10_players%", top10Html.toString());
				returnHtml = returnHtml.replace("%faction_war_time%", FactionWarManager.getInstance().getFactionWarTime());
				CommunityBoardHandler.separateAndSend(returnHtml, player);
				break;
			}
			case "_bbsfactiongve_dominance": {
				ZoneDominanceManager dominanceManager = ZoneDominanceManager.getInstance();
				FactionZoneManager zoneManager = FactionZoneManager.getInstance();
				List<FactionZone> zones = new ArrayList<>(ZoneManager.getInstance().getAllZones(FactionZone.class));

				// Calculate zone statistics
				int totalZones = zones.size();
				int waterZones = 0;
				int fireZones = 0;
				int activeZones = 0;

				for (FactionZone zone : zones) {
					if (zone.getControllingFaction() == Faction.WATER) {
						waterZones++;
					} else if (zone.getControllingFaction() == Faction.FIRE) {
						fireZones++;
					}

					if (zone.getStatus() == FactionZoneManager.ZoneStatus.ACTIVATED) {
						activeZones++;
					}
				}

				int neutralZones = totalZones - waterZones - fireZones;

				// Get dominance information
				double waterControlPercent = dominanceManager.getFactionZoneControlPercentage(Faction.WATER);
				double fireControlPercent = dominanceManager.getFactionZoneControlPercentage(Faction.FIRE);

				ZoneDominanceManager.DominanceTier waterTier = dominanceManager.getFactionDominanceTier(Faction.WATER);
				ZoneDominanceManager.DominanceTier fireTier = dominanceManager.getFactionDominanceTier(Faction.FIRE);

				String waterActiveBonuses = dominanceManager.getDominanceBonusesHtml(Faction.WATER);
				String fireActiveBonuses = dominanceManager.getDominanceBonusesHtml(Faction.FIRE);

				// Calculate next update time
				String nextUpdate = "In " + ((30 - (System.currentTimeMillis() / 60000) % 30)) + " minutes";

				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/gve/dominance.html");
				if (returnHtml == null) {
					returnHtml = "<html><body><br><br><center>Zone Dominance page is under maintenance.</center></body></html>";
				} else {
					returnHtml = returnHtml.replace("%water_control_percent%", String.valueOf(Math.round(waterControlPercent)));
					returnHtml = returnHtml.replace("%fire_control_percent%", String.valueOf(Math.round(fireControlPercent)));
					returnHtml = returnHtml.replace("%water_tier_name%", waterTier.getName());
					returnHtml = returnHtml.replace("%fire_tier_name%", fireTier.getName());
					returnHtml = returnHtml.replace("%water_tier_color%", waterTier.getColor());
					returnHtml = returnHtml.replace("%fire_tier_color%", fireTier.getColor());
					returnHtml = returnHtml.replace("%water_active_bonuses%", waterActiveBonuses);
					returnHtml = returnHtml.replace("%fire_active_bonuses%", fireActiveBonuses);
					returnHtml = returnHtml.replace("%total_zones%", String.valueOf(totalZones));
					returnHtml = returnHtml.replace("%water_zones%", String.valueOf(waterZones));
					returnHtml = returnHtml.replace("%fire_zones%", String.valueOf(fireZones));
					returnHtml = returnHtml.replace("%neutral_zones%", String.valueOf(neutralZones));
					returnHtml = returnHtml.replace("%active_zones%", String.valueOf(activeZones));
					returnHtml = returnHtml.replace("%next_update%", nextUpdate);
				}

				CommunityBoardHandler.separateAndSend(returnHtml, player);
				break;
			}
			case "_bbsfactiongve_info": {
				FactionZoneManager zoneManager = FactionZoneManager.getInstance();
				List<FactionZone> zones = new ArrayList<>(ZoneManager.getInstance().getAllZones(FactionZone.class));
				int controlledZones = 0;
				int activeZones = 0;
				for (FactionZone zone : zones) {
					if (zone.getControllingFaction() == player.getFaction()) {
						controlledZones++;
					}
					if (zone.getStatus() == FactionZoneManager.ZoneStatus.ACTIVATED) {
						activeZones++;
					}
				}
				// Calculate global ranking based on globalRankings
				List<FactionWarManager.PlayerRanking> globalRankings = FactionWarManager.getInstance().getGlobalRankings();
				int globalRank = 0;
				for (int i = 0; i < globalRankings.size(); i++) {
					if (globalRankings.get(i).getPlayer().getObjectId() == player.getObjectId()) {
						globalRank = i + 1;
						break;
					}
				}
				// Calculate faction ranking
				List<PlayerInstance> factionPlayers = World.getInstance().getPlayers().stream().filter(p -> p.getFaction() == player.getFaction()).collect(Collectors.toList());
				List<FactionWarManager.PlayerRanking> factionRankings = factionPlayers.stream().map(p -> new FactionWarManager.PlayerRanking(p, p.getPersonalPoints())).sorted(Comparator.comparingInt(FactionWarManager.PlayerRanking::getPoints).reversed()).collect(Collectors.toList());
				int factionRank = 0;
				for (int i = 0; i < factionRankings.size(); i++) {
					if (factionRankings.get(i).getPlayer().getObjectId() == player.getObjectId()) {
						factionRank = i + 1;
						break;
					}
				}
				// Determine global rank level
				String globalRankLevel = globalRank == 0 ? "None" : (globalRank == 1 ? "Top 1" : (globalRank <= 10 ? "Top 10" : (globalRank <= 50 ? "Top 50" : (globalRank <= 100 ? "Top 100" : (globalRank <= 300 ? "Top 300" : "Not Top")))));
				// Calculate and display reward information based on globalRank
				String rewardInfo;
				List<ItemHolder> rewards = new ArrayList<>();
				if (globalRank == 1 && !Config.FACTION_WAR_TOP_1_REWARD.isEmpty()) {
					rewards = Config.FACTION_WAR_TOP_1_REWARD;
					rewardInfo = "Top 1 Reward";
				} else if (globalRank <= 10 && !Config.FACTION_WAR_TOP_10_REWARD.isEmpty()) {
					rewards = Config.FACTION_WAR_TOP_10_REWARD;
					rewardInfo = "Top 10 Reward";
				} else if (globalRank <= 50 && !Config.FACTION_WAR_TOP_50_REWARD.isEmpty()) {
					rewards = Config.FACTION_WAR_TOP_50_REWARD;
					rewardInfo = "Top 50 Reward";
				} else if (globalRank <= 100 && !Config.FACTION_WAR_TOP_100_REWARD.isEmpty()) {
					rewards = Config.FACTION_WAR_TOP_100_REWARD;
					rewardInfo = "Top 100 Reward";
				} else if (globalRank <= 300 && !Config.FACTION_WAR_TOP_300_REWARD.isEmpty()) {
					rewards = Config.FACTION_WAR_TOP_300_REWARD;
					rewardInfo = "Top 300 Reward";
				} else {
					rewardInfo = "No Reward";
				}
				// Generate reward details
				StringBuilder rewardDetails = new StringBuilder();
				if (!rewards.isEmpty()) {
					rewardDetails.append("<font color=\"32cd32\">").append(rewardInfo).append("</font><br1>");
					for (ItemHolder reward : rewards) {
						Item item = ItemTable.getInstance().getTemplate(reward.getId());
						if (item != null) {
							rewardDetails.append("- ").append(item.getName()).append(": x").append(reward.getCount()).append("<br1>");
						} else {
							rewardDetails.append("- Unknown Item (ID: ").append(reward.getId()).append("): ").append(reward.getCount()).append("<br1>");
						}
					}
				} else {
					rewardDetails.append("<font color=\"FF0000\">No Reward</font>");
				}
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/gve/info.html");
				returnHtml = returnHtml.replace("%faction%", player.getFaction() == Faction.FIRE ? "Fire" : (player.getFaction() == Faction.WATER ? "Water" : "None"));
				returnHtml = returnHtml.replace("%personal_points%", String.valueOf(player.getPersonalPoints()));
				returnHtml = returnHtml.replace("%trustLevel%", String.valueOf(player.getTrustLevel()));
				returnHtml = returnHtml.replace("%rank_level%", globalRank == 0 ? "Not Ranked" : "#" + globalRank + " (Faction Rank: #" + factionRank + ")");
				returnHtml = returnHtml.replace("%reward_info%", rewardDetails.toString());
				returnHtml = returnHtml.replace("%active_zones%", String.valueOf(activeZones));
				returnHtml = returnHtml.replace("%elite_boss_points%", String.valueOf(Config.FACTION_WAR_ELITE_BOSS_KILL_POINTS));
				CommunityBoardHandler.separateAndSend(returnHtml, player);
				break;
			}
			case "factiongve_skill_list": {
				int page = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
				int skillsPerPage = 5;
				int trustLevel = player.getTrustLevel();
				// Lấy tất cả kỹ năng bằng getSkillList()
				List<FactionWarSkill> allSkills = FactionWarSkillHolder.getInstance().getSkillList();
				int totalSkills = allSkills.size();
				int totalPages = totalSkills > 0 ? (int) Math.ceil((double) totalSkills / skillsPerPage) : 0;
				page = Math.max(0, Math.min(page, totalPages - 1));
				// Prepare the skill list
				StringBuilder skillListHtml = new StringBuilder();
				if (totalSkills == 0) {
					skillListHtml.append("<table cellpadding=\"0\" cellspacing=\"0\" height=\"100\" width=\"290\">");
					skillListHtml.append("<tr>");
					skillListHtml.append("<td align=\"center\">");
					skillListHtml.append("<font color=\"ffa500\">");
					skillListHtml.append("No skills available at the moment!");
					skillListHtml.append("</font>");
					skillListHtml.append("</td>");
					skillListHtml.append("</tr>");
					skillListHtml.append("</table>");
				} else {
					int startIndex = page * skillsPerPage;
					int endIndex = Math.min(startIndex + skillsPerPage, totalSkills);
					for (int i = startIndex; i < endIndex; i++) {
						FactionWarSkill skill = allSkills.get(i);
						Skill skillEntry = SkillData.getInstance().getSkill(skill.getSkillId(), skill.getSkillLevel());
						if (skillEntry == null)
							continue;
						skillListHtml.append("<table width=\"290\" height=\"32\" cellpadding=\"0\" cellspacing=\"0\" bgcolor=\"").append(i % 2 == 0 ? "1c1b17" : "2f322f").append("\">");
						skillListHtml.append("<tr>");
						skillListHtml.append("<td align=\"center\" fixwidth=\"5\">");
						skillListHtml.append("<img src=\"").append(skillEntry.getIcon()).append("\" width=\"32\" height=\"32\"/>");
						skillListHtml.append("</td>");
						skillListHtml.append("<td align=\"left\" fixwidth=\"25\">");
						skillListHtml.append("  ").append(skillEntry.getName());
						skillListHtml.append("</td>");
						int currentLevel = player.getFactionWarSkillLevel(skill.getSkillId());
						if (currentLevel >= skill.getSkillLevel()) {
							skillListHtml.append("<td align=\"center\" fixwidth=\"5\">");
							skillListHtml.append("<font color=\"LEVEL\">").append(skill.getSkillLevel()).append("</font>");
							skillListHtml.append("</td>");
							skillListHtml.append("<td align=\"center\" fixwidth=\"10\">");
							skillListHtml.append("<font color=\"32cd32\">MAX</font>");
							skillListHtml.append("</td>");
						} else {
							skillListHtml.append("<td align=\"center\" fixwidth=\"5\">");
							skillListHtml.append("<font color=\"LEVEL\">").append(currentLevel).append("</font>");
							skillListHtml.append("</td>");
							skillListHtml.append("<td align=\"center\" fixwidth=\"10\">");
							// Kiểm tra trustLevel để hiển thị nút hoặc thông báo
							if (trustLevel >= skill.getMinTrust()) {
								skillListHtml.append("<button value=\"+\" action=\"bypass factiongve_skill_view ").append(skill.getSkillId()).append(" ").append(skill.getSkillLevel()).append("\" width=32 height=32 back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\" />");
							} else {
								skillListHtml.append("<font color=\"FF0000\">(Trust ").append(skill.getMinTrust()).append(" required)</font>");
							}
							skillListHtml.append("</td>");
						}
						skillListHtml.append("</tr>");
						skillListHtml.append("</table>");
					}
				}
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/gve/fw_skill_list.html");
				if (returnHtml == null) {
					LOGGER.warning("Could not load HTML file: data/html/CommunityBoard/KhoaCustom/gve/fw_skill_list.html");
					player.sendMessage("Error: Could not load skill list interface.");
					break;
				}
				// Replace placeholders
				returnHtml = returnHtml.replace("%skill_list%", skillListHtml.toString());
				returnHtml = returnHtml.replace("%current_page%", String.valueOf(page + 1));
				returnHtml = returnHtml.replace("%total_pages%", String.valueOf(totalPages));
				returnHtml = returnHtml.replace("%prev_page%", page > 0 ? "<button value=\"<\" action=\"bypass factiongve_skill_list " + (page - 1) + "\" width=24 height=20 back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\" />" : "");
				returnHtml = returnHtml.replace("%next_page%", page < totalPages - 1 ? "<button value=\">\" action=\"bypass factiongve_skill_list " + (page + 1) + "\" width=24 height=20 back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\" />" : "");
				CommunityBoardHandler.separateAndSend(returnHtml, player);
				break;
			}
			case "factiongve_skill_view": {
				int skillId = Integer.parseInt(parts[1]);
				int skillLevel = Integer.parseInt(parts[2]);
				FactionWarSkill skill = FactionWarSkillHolder.getInstance().getSkill(skillId, skillLevel);
				if (skill == null) {
					player.sendMessage("Skill not found!");
					break;
				}
				Skill skillEntry = SkillData.getInstance().getSkill(skillId, skillLevel);
				if (skillEntry == null) {
					player.sendMessage("Error: Skill not available!");
					break;
				}
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/gve/fw_skill_learn.html");
				if (returnHtml == null) {
					LOGGER.warning("Could not load HTML file: data/html/CommunityBoard/KhoaCustom/gve/fw_skill_learn.html");
					player.sendMessage("Error: Could not load skill learn interface.");
					break;
				}
				// Replace placeholders
				returnHtml = returnHtml.replace("%skill_id%", String.valueOf(skillId));
				returnHtml = returnHtml.replace("%skill_level%", String.valueOf(skillLevel));
				returnHtml = returnHtml.replace("%skill_name%", skillEntry.getName());
				returnHtml = returnHtml.replace("%icon%", skillEntry.getIcon());
				returnHtml = returnHtml.replace("%desc%", "Increases " + skillEntry.getName() + " effect.");
				returnHtml = returnHtml.replace("%cost%", String.valueOf(skill.getCost()));
				returnHtml = returnHtml.replace("%available_points%", String.valueOf(player.getPersonalPoints()));
				CommunityBoardHandler.separateAndSend(returnHtml, player);
				break;
			}
			case "factiongve_learn_skill": {
				int skillId = Integer.parseInt(parts[1]);
				int skillLevel = Integer.parseInt(parts[2]);
				FactionWarSkill skill = FactionWarSkillHolder.getInstance().getSkill(skillId, skillLevel);
				if (skill == null) {
					player.sendMessage("Skill not found!");
					break;
				}
				int trustLevel = player.getTrustLevel();
				if (trustLevel < skill.getMinTrust()) {
					player.sendMessage("Your Trust Level is too low to learn this skill!");
					break;
				}
				int currentLevel = player.getFactionWarSkillLevel(skillId);
				if (currentLevel + 1 != skillLevel) {
					player.sendMessage("You must learn the previous level of this skill first!");
					break;
				}
				if (!player.usePersonalPoints(skill.getCost())) {
					player.sendMessage("Not enough available GvE points to learn this skill!");
					break;
				}
				Skill skillEntry = SkillData.getInstance().getSkill(skillId, skillLevel);
				if (skillEntry == null) {
					player.sendMessage("Error: Skill not available!");
					break;
				}
				player.addFactionWarSkill(skillEntry);
				player.sendMessage("You have learned " + skillEntry.getName() + " Level " + skillLevel + "!");
				// Redirect to the skill list
				parseCommunityBoardCommand("factiongve_skill_list 0", player);
				break;
			}
			case "faction_change_personal": {
				// Check if the player can change faction
				boolean success = FactionBalanceService.getInstance().changePersonalFaction(player);
				if (success) {
					player.sendMessage("Faction changed successfully!");
				}
				// Redirect back to the GvE home page
				parseCommunityBoardCommand("_bbsfactiongve 0", player);
				break;
			}
			case "faction_change_clan": {
				// Check if the player can change clan faction
				boolean success = FactionBalanceService.getInstance().changeClanFaction(player);
				if (success) {
					player.sendMessage("Clan faction changed successfully!");
				}
				// Redirect back to the GvE home page
				parseCommunityBoardCommand("_bbsfactiongve 0", player);
				break;
			}
			case "_bbsartifacts": {
				// Generate HTML for Artifacts
				StringBuilder artifactsHtml = new StringBuilder();
				List<ArtifactEntity> artifacts = new ArrayList<>(ArtifactManager.getInstance().getArtifacts());
				if (artifacts.isEmpty()) {
					artifactsHtml.append("<tr>");
					artifactsHtml.append("<td align=\"left\" width=\"320\" height=\"30\">");
					artifactsHtml.append("<font color=\"FFFFFF\">No artifacts available</font>");
					artifactsHtml.append("</td>");
					artifactsHtml.append("</tr>");
				} else {
					for (int i = 0; i < artifacts.size(); i++) {
						ArtifactEntity artifact = artifacts.get(i);
						if (artifact.getArtifact() == null)
							continue; // Skip if Artifact does not exist
						artifactsHtml.append("<tr>");
						artifactsHtml.append("<td align=\"left\" width=\"320\" height=\"25\">");
						artifactsHtml.append("<table width=\"320\" cellpadding=\"0\" cellspacing=\"0\"><tr>");
						artifactsHtml.append("<td width=\"250\" align=\"left\">");
						artifactsHtml.append("<font name=\"hs09\" color=\"FFFFFF\">").append(artifact.getName(player)).append("</font>");
						Faction faction = artifact.getFraction();
						if (faction == Faction.FIRE) {
							artifactsHtml.append(" <font color=\"FF0000\">(Owner by Fire)</font>");
						} else if (faction == Faction.WATER) {
							artifactsHtml.append(" <font color=\"48BEFA\">(Owner by Water)</font>");
						} else {
							artifactsHtml.append(" <font color=\"FFFFFF\">(Neutral)</font>");
						}
						artifactsHtml.append("</td>");
						artifactsHtml.append("<td width=\"70\" align=\"right\">");
						// Check conditions to display the teleport button
						boolean canTeleport = faction == player.getFaction() || faction == Faction.NONE;
						if (canTeleport) {
							artifactsHtml.append("<button value=\"Teleport\" action=\"bypass artifact_teleport ").append(artifact.getTemplate().getId()).append("\" width=\"60\" height=\"20\" back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">");
						}
						artifactsHtml.append("</td>");
						artifactsHtml.append("</tr></table>");
						artifactsHtml.append("</td>");
						artifactsHtml.append("</tr>");
					}
				}
				// Load and replace HTML
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/gve/home.html");
				returnHtml = returnHtml.replace("%online%", String.valueOf(World.getInstance().getPlayers().size()));
				returnHtml = returnHtml.replace("%artifacts%", artifactsHtml.toString());
				CommunityBoardHandler.separateAndSend(returnHtml, player);
				break;
			}
			case "artifact_teleport": {
				int artifactId = Integer.parseInt(parts[1]);
				ArtifactEntity artifact = ArtifactManager.getInstance().getArtifacts().stream().filter(a -> a.getTemplate().getId() == artifactId).findFirst().orElse(null);
				if (artifact == null || artifact.getArtifact() == null) {
					player.sendMessage("Artifact not found or not available.");
					break;
				}
				// Check conditions to display the teleport button
				Faction artifactFaction = artifact.getFraction();
				// Log only if LOG_FACTION_DETAILS is enabled
				if (Config.LOG_FACTION_DETAILS) {
					LOGGER.info("Teleport attempt: Artifact ID " + artifactId + " faction: " + artifactFaction + ", player faction: " + player.getFaction());
				}
				if (artifactFaction != Faction.NONE && artifactFaction != player.getFaction()) {
					player.sendMessage("You cannot teleport to an Artifact controlled by an enemy faction.");
					break;
				}
				// Check teleport conditions
				if (player.isInCombat()) {
					player.sendMessage("You cannot teleport while being in fight.");
					break;
				}
				if (GabrielEvents.isInEvent(player)) {
					player.sendMessage("You cannot teleport while being in event.");
					break;
				}
				if (player.isInOlympiadMode() || OlympiadManager.getInstance().isRegisteredInComp(player)) {
					player.sendMessage("You cannot teleport while being in Olympiad.");
					break;
				}
				// Get the center location of the artifact
				Location centerLoc = artifact.getTemplate().getLocation();
				if (centerLoc == null) {
					player.sendMessage("Artifact location is not available.");
					break;
				}
				// Calculate a random teleport location 2000 units away from the center
				double radius = 1500.0 + Rnd.get(0, 500); // 1500-2000 units for artifacts

				double angle = Rnd.get(0, 360) * Math.PI / 180.0;
				int newX = centerLoc.getX() + (int) (radius * Math.cos(angle));
				int newY = centerLoc.getY() + (int) (radius * Math.sin(angle));
				int newZ = centerLoc.getZ();
				newZ = GeoEngine.getInstance().getHeight(newX, newY, newZ);
				Location teleportLoc = new Location(newX, newY, newZ);
				// Perform teleport immediately
				//player.teleToLocation(teleportLoc, 0);
				player.abortCast();
				player.stopMove(null);
				player.setTeleportLoc(teleportLoc);
				if (player.isCastingNow()) {
					player.setQueuedSkill(CommonSkill.TELEPORT.getSkill(), null, false, false);
				} else {
					player.doCast(CommonSkill.TELEPORT.getSkill());
				}
				player.sendMessage("You have been teleported near the Artifact of " + artifact.getName(player) + "!");
				break;
			}
			case "_bbschangepass": {
				if ((player.getSecretCode() == null) || player.getSecretCode().equalsIgnoreCase("")) // doesn't have a secret code set
				{
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/password/setsecretcode.html");
					returnHtml = returnHtml.replace("%dtn%", "You don't have an account secret code set, you must set it first before you can change your password.");
					returnHtml = replaceVars(player, returnHtml);
				} else {
					// has a secret code set
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/password/passchangemain.html");
					returnHtml = returnHtml.replace("%dtn%", " ");
					returnHtml = replaceVars(player, returnHtml);
				}
				// Send HTML directly like other cases
				if (returnHtml != null) {
					CommunityBoardHandler.separateAndSend(returnHtml, player);
				}
				return true; // Return early to prevent double sending
			}
			case "_bbschangepass_action": {
				final String errorMsg = doPasswordChange(player, command);
				if (errorMsg != null) {
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/password/passchangemain.html");
					returnHtml = returnHtml.replace("%dtn%", errorMsg);
				} else {
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/password/passchangemain-done.html");
				}
				returnHtml = replaceVars(player, returnHtml);
				break;
			}
			case "_bbssetsecret_action": {
				final String errorMsg = setSecretCode(player, command);
				if (errorMsg != null) {
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/password/setsecretcode.html");
					returnHtml = returnHtml.replace("%dtn%", errorMsg);
					returnHtml = replaceVars(player, returnHtml);
				} else {
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/password/setsecretcode-done.html");
					player.setLockdownTime(0);
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("UPDATE accounts SET lockdowntime=? WHERE login=?")) {
						statement.setInt(1, 0);
						statement.setString(2, player.getAccountName());
						statement.execute();
						statement.close();
					} catch (Exception e) {
						LOGGER.log(Level.SEVERE, "Failed setting lockdown time", e);
					}
					returnHtml = replaceVars(player, returnHtml);
				}
				break;
			}
			case "_bbschangesecret": {
				if ((player.getSecretCode() == null) || player.getSecretCode().equalsIgnoreCase("")) // doesn't have a secret code set
				{
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/password/setsecretcode.html");
					returnHtml = returnHtml.replace("%dtn%", "You don't have a secret code set to begin with, you can set it here.");
					returnHtml = replaceVars(player, returnHtml);
				} else {
					// has a secret code set
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/password/changesecretcode.html");
					returnHtml = returnHtml.replace("%dtn%", "");
					returnHtml = replaceVars(player, returnHtml);
				}
				break;
			}
			case "_bbssuccess_secret": {
				if ((player.getClan() != null) && player.getClan().isNoticeEnabled()) {
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/success-secret.html");
					returnHtml = returnHtml.replace("%clan_name%", player.getClan().getName());
					returnHtml = returnHtml.replace("%notice_text%", player.getClan().getNotice());
					returnHtml = replaceVars(player, returnHtml);
				} else {
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/home.html");
					returnHtml = replaceVars(player, returnHtml);
				}
				break;
			}
			case "_bbschangesecret_action": {
				final String errorMsg = setSecretCode(player, command);
				if (errorMsg != null) {
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/password/changesecretcode.html");
					returnHtml = returnHtml.replace("%dtn%", errorMsg);
					returnHtml = replaceVars(player, returnHtml);
				} else {
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/password/changesecretcode-done.html");
					returnHtml = replaceVars(player, returnHtml);
				}
				break;
			}
			case "_bbsconfirmsecret_action": {
				final boolean secretOk = isSecretCodeConfirmed(player, command);
				if (!secretOk) {
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/password/secretcodeconfirmation.html");
					returnHtml = replaceVars(player, returnHtml);
				} else {
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/password/secretcodeconfirmation-done.html");
					player.sendMessage("Your character is now fully functional. Thank you!");
					player.setLockdownTime(0);
					returnHtml = replaceVars(player, returnHtml);
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("UPDATE accounts SET lockdowntime=? WHERE login=?")) {
						statement.setInt(1, 0);
						statement.setString(2, player.getAccountName());
						statement.execute();
						statement.close();
					} catch (Exception e) {
						LOGGER.log(Level.SEVERE, "Failed setting lockdown time", e);
					}
				}
				break;
			}
			case "_bbsforgot_secret_code": {
				// Trực tiếp gửi recovery code qua Discord và set làm secret code mới
				if (sendRecoveryDiscord(player)) {
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/password/secretcodeconfirmation.html");
					returnHtml = replaceVars(player, returnHtml);
					player.sendMessage("New secret code has been sent to your Discord DM and automatically set!");
					player.sendMessage("You can now use the new secret code or change it to something else.");
				} else {
					returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/home.html");
					returnHtml = replaceVars(player, returnHtml);
				}
				break;
			}
			case "_bbsstat": {
				DecimalFormat df = new DecimalFormat("#.#");
				StringBuilder statListHtml = new StringBuilder();
				// Ánh xạ stat tùy chỉnh với mode PER hoặc DIFF
				class CustomStatInfo {
					final Stat stat;
					final StatModifierType mode;

					CustomStatInfo(Stat stat, StatModifierType mode) {
						this.stat = stat;
						this.mode = mode;
					}
				}
				CustomStatInfo[] customStats =
						{
								new CustomStatInfo(Stat.ABSORB_CP_PERCENT, StatModifierType.PER),
								new CustomStatInfo(Stat.EVASION_ABSOLUTE, StatModifierType.DIFF),
								new CustomStatInfo(Stat.PERF_BLOCK_ADD, StatModifierType.DIFF),
								new CustomStatInfo(Stat.SHIELD_DEFENCE_ANGLE, StatModifierType.DIFF),
								new CustomStatInfo(Stat.POWER_ATTACK_ANGLE, StatModifierType.DIFF),
								new CustomStatInfo(Stat.CHAIN_SHOT, StatModifierType.DIFF),
								new CustomStatInfo(Stat.ROBE_DAM_MUL, StatModifierType.PER),
								new CustomStatInfo(Stat.HEAVY_DAM_MUL, StatModifierType.PER),
								new CustomStatInfo(Stat.LIGHT_DAM_MUL, StatModifierType.PER),
								new CustomStatInfo(Stat.MORE_DEBUFF, StatModifierType.DIFF),
								new CustomStatInfo(Stat.LESS_DEBUFF, StatModifierType.DIFF),
								new CustomStatInfo(Stat.RUSH_DIST_ADD, StatModifierType.DIFF),
								new CustomStatInfo(Stat.EVADE_AOE_SPELL, StatModifierType.DIFF),
								new CustomStatInfo(Stat.EVADE_AOE_HIT, StatModifierType.DIFF),
								new CustomStatInfo(Stat.INC_DAM_MP, StatModifierType.DIFF),
								new CustomStatInfo(Stat.INC_DAM_HP, StatModifierType.DIFF),
								new CustomStatInfo(Stat.INC_DAM_CP, StatModifierType.DIFF),
								new CustomStatInfo(Stat.CRITICAL_DMG_ADD_BLEEDING, StatModifierType.DIFF),
								new CustomStatInfo(Stat.P_SKILL_EVASION_REDUCTION, StatModifierType.DIFF),
								new CustomStatInfo(Stat.M_SKILL_EVASION_REDUCTION, StatModifierType.DIFF),
								new CustomStatInfo(Stat.SEE_INVISIBLE, StatModifierType.DIFF),
								new CustomStatInfo(Stat.SKILL_RADIUS_BOOST, StatModifierType.DIFF),
								new CustomStatInfo(Stat.CRIT_MAX_ADD, StatModifierType.DIFF),
								new CustomStatInfo(Stat.DMG_ADD, StatModifierType.DIFF),
								new CustomStatInfo(Stat.DMG_REMOVE, StatModifierType.DIFF),
								new CustomStatInfo(Stat.CAST_SPEED_MAX_ADD, StatModifierType.DIFF),
								new CustomStatInfo(Stat.RESIST_REMOVE_TARGET, StatModifierType.DIFF),
								new CustomStatInfo(Stat.SKILL_REUSE_CHANGE, StatModifierType.DIFF),
								new CustomStatInfo(Stat.TANK_ARCHERY, StatModifierType.DIFF),
								new CustomStatInfo(Stat.TANK_SPELLS, StatModifierType.DIFF),
								new CustomStatInfo(Stat.POWER_DEFENCE_BEHIND, StatModifierType.PER),
								new CustomStatInfo(Stat.MAGIC_DEFENCE_BEHIND, StatModifierType.PER),
								new CustomStatInfo(Stat.HAMSTRING, StatModifierType.DIFF),
								new CustomStatInfo(Stat.BOW_AOE_RADIUS, StatModifierType.DIFF),
								new CustomStatInfo(Stat.BOW_AOE_DMG_MOD, StatModifierType.DIFF),
								new CustomStatInfo(Stat.AOE_HIT_OTHER_PERCENT, StatModifierType.DIFF),
								new CustomStatInfo(Stat.BACK_ANGLE_INCREASE, StatModifierType.DIFF),
								new CustomStatInfo(Stat.RANGED_PIERCING, StatModifierType.DIFF),
								new CustomStatInfo(Stat.PDEF_IGNORE, StatModifierType.PER),
								new CustomStatInfo(Stat.MDEF_IGNORE, StatModifierType.PER),
								new CustomStatInfo(Stat.PDEF_REDUCE, StatModifierType.PER),
								new CustomStatInfo(Stat.MDEF_REDUCE, StatModifierType.PER),
								new CustomStatInfo(Stat.DEMONIC_MOVEMENT, StatModifierType.DIFF),
								new CustomStatInfo(Stat.REFLECT_ARROWS, StatModifierType.DIFF),
								new CustomStatInfo(Stat.RANGE_DMG_DIST_BOOST, StatModifierType.DIFF),
								new CustomStatInfo(Stat.RANGE_DMG_DIST_BOOST_SKILL, StatModifierType.DIFF),
								new CustomStatInfo(Stat.DAMAGE_STRAIGHT_TO_HP, StatModifierType.DIFF),
								new CustomStatInfo(Stat.OVERPOWER, StatModifierType.DIFF),
								new CustomStatInfo(Stat.OVERDRIVE, StatModifierType.DIFF),
								new CustomStatInfo(Stat.LIONHEART, StatModifierType.DIFF),
								new CustomStatInfo(Stat.IGNORE_SHIELD, StatModifierType.DIFF),
								new CustomStatInfo(Stat.PDAM_MAX, StatModifierType.DIFF),
								new CustomStatInfo(Stat.MDAM_MAX, StatModifierType.DIFF),
								new CustomStatInfo(Stat.SAME_RACE_DMG_VUL, StatModifierType.PER),
								new CustomStatInfo(Stat.SAME_RACE_DMG_VUL, StatModifierType.DIFF),
								new CustomStatInfo(Stat.HATED_RACE_DMG_BOOST, StatModifierType.PER),
								new CustomStatInfo(Stat.HATED_RACE_DMG_BOOST, StatModifierType.DIFF)
						};
				// Phân trang
				int statsPerPage = 20; // Số stat mỗi trang
				int totalStats = customStats.length;
				int totalPages = totalStats > 0 ? (int) Math.ceil((double) totalStats / statsPerPage) : 1;
				int page = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
				page = Math.max(0, Math.min(page, totalPages - 1)); // Đảm bảo page trong khoảng hợp lệ
				// Tạo HTML cho danh sách stat trên trang hiện tại
				int startIndex = page * statsPerPage;
				int endIndex = Math.min(startIndex + statsPerPage, totalStats);
				for (int i = startIndex; i < endIndex; i++) {
					CustomStatInfo statInfo = customStats[i];
					double value;
					if (statInfo.mode == StatModifierType.PER) {
						value = (player.getStat().getMul(statInfo.stat) - 1) * 100; // Tính phần trăm
					} else {
						value = player.getStat().getAdd(statInfo.stat); // Tính giá trị cộng
					}
					String displayValue = statInfo.mode == StatModifierType.PER ? df.format(value) + "%" : df.format(value);
					statListHtml.append("<tr>");
					statListHtml.append("<td width=\"300\" align=\"left\"><font name=\"hs09\" color=\"FFFFFF\">").append(formatStatName(statInfo.stat.name())).append("</font></td>");
					statListHtml.append("<td width=\"100\" align=\"right\"><font name=\"hs09\" color=\"LEVEL\">").append(displayValue).append("</font></td>");
					statListHtml.append("</tr>");
					// Thêm dấu phân cách bằng hình ảnh, trừ stat cuối cùng trên trang
					if (i < endIndex - 1) {
						statListHtml.append("<tr><td colspan=\"2\" align=\"center\">").append("<img src=\"L2UI_CT1.SiegeReport_dividerH\" width=\"200\" height=\"3\">").append("</td></tr>");
					}
				}
				// Tạo nút phân trang
				String prevPageHtml = page > 0 ? "<button value=\"Previous\" action=\"bypass _bbsStat " + (page - 1) + "\" width=\"60\" height=\"20\" back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">" : "";
				String nextPageHtml = page < totalPages - 1 ? "<button value=\"Next\" action=\"bypass _bbsStat " + (page + 1) + "\" width=\"60\" height=\"20\" back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">" : "";
				String pageInfo = "Page " + (page + 1) + " of " + totalPages;
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/stats/custom.htm");
				if (returnHtml == null) {
					LOGGER.warning("Could not load HTML file: data/html/CommunityBoard/KhoaCustom/stats/custom.htm");
					player.sendMessage("Error: Could not load custom stats interface.");
					break;
				}
				returnHtml = returnHtml.replace("%custom_stats%", statListHtml.toString());
				returnHtml = returnHtml.replace("%prev_page%", prevPageHtml);
				returnHtml = returnHtml.replace("%next_page%", nextPageHtml);
				returnHtml = returnHtml.replace("%page_info%", pageInfo);
				CommunityBoardHandler.separateAndSend(returnHtml, player);
				break;
			}
			case "_bbstop_settings_player_block_down_504": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/menu/player.htm");
				if (!player.isAccountLockedDown()) {
					setTimeLockDown(player, command);
					player.broadcastUserInfo();
				} else {
					player.sendLockdownTime();
					player.broadcastUserInfo();
				}
				player.sendPacket(new NpcHtmlMessage(dialog));
				break;
			}
			case "_bbspremium": {
				try {
					String html = HtmCache.getInstance().getHtm(player, "data/html/premium/index.htm");
					if (html == null) {
						player.sendMessage("Error: Premium index.htm not found!");
						return false;
					}

					StringBuilder button = new StringBuilder();
					String template = HtmCache.getInstance().getHtm(player, "data/html/premium/button.htm");
					if (template == null) {
						player.sendMessage("Error: Premium button.htm not found!");
						return false;
					}

					Collection<PremiumAccount> premiums = PremiumData.getInstance().getAllPremiums();
					if (premiums == null || premiums.isEmpty()) {
						player.sendMessage("Error: No premium packages found!");
						button.append("<tr><td align=center><font color=FFFF00>No premium packages available</font></td></tr>");
					} else {
						for (PremiumAccount premium : premiums) {
							String block = template;
							block = block.replace("{name}", premium.getName());
							block = block.replace("{icon}", premium.getIcon());
							block = block.replace("{time}", Util.formatTime(premium.getTime()));
							if (premium.getPriceId() == -1) {
								block = block.replace("{price_text}", String.format("<font color=99CC66>Cost:</font> %d Pi", premium.getPriceCount()));
							} else {
								block = block.replace("{price_text}", String.format("<font color=99CC66>Cost:</font> %d %s", premium.getPriceCount(), ItemTable.getInstance().getTemplate(premium.getPriceId()).getName()));
							}
							block = block.replace("{link}", "bypass _bbspremiuminfo " + premium.getId());
							button.append(block);
						}
					}

					html = html.replace("{body}", button.toString());
					html = replaceVars(player, html);
					player.sendPacket(new NpcHtmlMessage(html));
				} catch (Exception e) {
					player.sendMessage("Error loading premium page: " + e.getMessage());
					e.printStackTrace();
				}
				break;
			}
			case "_bbspremiuminfo": {
				int id = Integer.parseInt(parts[1]);
				String html = HtmCache.getInstance().getHtm(player, "data/html/premium/info.htm");
				PremiumAccount premium = PremiumData.getInstance().getPremium(id);
				if (html == null) {
					player.sendMessage("Error: Could not load premium info page.");
					break;
				}
				if (premium == null) {
					player.sendMessage("Premium package not found.");
					break;
				}
				html = html.replace("{name}", premium.getName());
				html = html.replace("{time}", Util.formatTime(premium.getTime()));
				// Hiển thị giá bằng PrimePoints hoặc Item tùy thuộc vào priceId
				if (premium.getPriceId() == -1) {
					html = html.replace("{price_text}", String.format("<font color=99CC66>Cost:</font> %d Pi", premium.getPriceCount()));
				} else {
					html = html.replace("{price_text}", String.format("<font color=99CC66>Cost:</font> %d %s", premium.getPriceCount(), ItemTable.getInstance().getTemplate(premium.getPriceId()).getName()));
				}
				html = html.replace("{xp}", "+" + formatPercent((premium.getExp() - 1) * 100));
				html = html.replace("{sp}", "+" + formatPercent((premium.getSp() - 1) * 100));
				html = html.replace("{adena}", "+" + formatPercent((premium.getAdena() - 1) * 100));
				html = html.replace("{items}", "+" + formatPercent((premium.getDropChance() - 1) * 100));
				html = html.replace("{spoil}", "+" + formatPercent((premium.getSpoilChance() - 1) * 100));
				html = html.replace("{lcoin}", "+" + formatPercent(premium.getLCoinDropAmountAdd() * 100));
				html = html.replace("{craft}", "+" + formatPercent((premium.getRandomCraftHerb() - 1) * 100));
				html = html.replace("{sayha}", "+" + formatPercent((premium.getSayhaGraceXp() - 1) * 100));
				html = html.replace("{limit_sayha}", "+" + formatPercent((premium.getLimitedSayhaGraceXp() - 1) * 100));
				html = html.replace("{res_cost}", "+" + formatPercent((premium.getResurrectionCost() - 1) * 100));
				html = html.replace("{resurrection_cost}", formatPercent((1 - premium.getResurrectionCost()) * 100) + "% discount");
				html = html.replace("{auto_res_daily}", club.projectessence.Config.AUTOPLAY_AUTO_RESURRECTION_MAX_USES_PER_DAY_PREMIUM + " times (vs " + club.projectessence.Config.AUTOPLAY_AUTO_RESURRECTION_MAX_USES_PER_DAY + " regular)");
				long currentExpiration = PremiumManager.getInstance().getPremiumExpiration(player.getObjectId());
				if (currentExpiration > System.currentTimeMillis()) {
					html = html.replace("{current_status}", "Active until " + new SimpleDateFormat("dd.MM.yyyy HH:mm").format(new Date(currentExpiration)));
				} else {
					html = html.replace("{current_status}", "Not active");
				}
				// Thay thế các placeholder khác trong HTML (nếu có)
				html = html.replace("{xp_f}", String.format("%.2f", premium.getExp()));
				html = html.replace("{sp_f}", String.format("%.2f", premium.getSp()));
				html = html.replace("{adena_f}", String.format("%.2f", premium.getAdena()));
				html = html.replace("{items_f}", String.format("%.2f", premium.getDropChance()));
				html = html.replace("{spoil_f}", String.format("%.2f", premium.getSpoilChance()));
				html = html.replace("{lcoin_f}", String.format("%.2f", premium.getLCoinDropAmountAdd()));
				html = html.replace("{craft_f}", String.format("%.2f", premium.getRandomCraftHerb()));
				html = html.replace("{sayha_f}", "+" + formatPercent((premium.getSayhaGraceXp() - 1) * 100));
				html = html.replace("{limit_sayha_f}", "+" + formatPercent((premium.getLimitedSayhaGraceXp() - 1) * 100));
				html = html.replace("{res_cost_f}", "+" + formatPercent((premium.getResurrectionCost() - 1) * 100));
				html = html.replace("{icon}", premium.getIcon());
				html = html.replace("{id}", String.valueOf(id));
				html = replaceVars(player, html);
				// player.sendPacket(new NpcHtmlMessage(html));
				player.sendPacket(new NpcHtmlMessage(html));
				break;
			}
			case "_bbspremiumbuy": {
				// Kiểm tra xem parts[1] có tồn tại và là số hợp lệ không
				if (parts.length < 2) {
					player.sendMessage("Invalid command: Premium package ID is missing.");
					break;
				}
				int id;
				try {
					id = Integer.parseInt(parts[1]);
				} catch (NumberFormatException e) {
					player.sendMessage("Invalid command: Premium package ID must be a number.");
					LOGGER.warning("Player " + player.getName() + " sent invalid premium buy command: " + command);
					break;
				}
				PremiumAccount premium = PremiumData.getInstance().getPremium(id);
				if (premium == null) {
					player.sendMessage("Premium package not found.");
					break;
				}
				// Lấy thời gian hiện tại
				Calendar currentTime = Calendar.getInstance(); // Không cần setTimeZone, vì múi giờ mặc định đã là +07
				long current = currentTime.getTimeInMillis(); // Mili-giây
				long bonusExpire = PremiumManager.getInstance().getPremiumExpiration(player.getObjectId()); // Mili-giây
				int currentPremiumId = PremiumManager.getInstance().getPremiumId(player.getObjectId()); // Lấy ID gói Premium hiện tại
				PremiumAccount currentPremium = PremiumData.getInstance().getPremium(currentPremiumId); // Lấy thông tin gói Premium hiện tại
				// Kiểm tra nếu người chơi đã có gói Premium còn hạn
				if (bonusExpire > current) {
					// Trường hợp 1: Gói mới có level cao hơn -> Nâng cấp và tăng thời gian hết hạn
					if (currentPremium != null && id > currentPremiumId) {
						int priceId = premium.getPriceId();
						long priceCount = premium.getPriceCount();
						int time = premium.getTime(); // time là giây
						// Kiểm tra và trừ PrimePoints
						if (priceId == -1) // Sử dụng PrimePoints
						{
							if (player.getPrimePoints() < priceCount) {
								player.sendMessage("You do not have enough PrimePoints to upgrade your Premium package. You need " + priceCount + " PrimePoints.");
								break;
							}
							// Trừ PrimePoints
							player.setPrimePoints((int) (player.getPrimePoints() - priceCount));
						} else // Sử dụng item (hỗ trợ tương thích ngược nếu cần)
						{
							ItemInstance itemInstance = player.getInventory().getItemByItemId(priceId);
							if (itemInstance == null || itemInstance.getCount() < priceCount) {
								player.sendMessage("You do not have enough required items to upgrade your Premium package.");
								break;
							}
							if (player.getInventory().destroyItemByItemId("PremiumUpgrade", priceId, priceCount, player, null) == null) {
								player.sendMessage("Failed to deduct the required items. Please try again.");
								break;
							}
						}
						// Tính thời gian hết hạn mới bằng Calendar
						Calendar expiration = Calendar.getInstance();
						expiration.setTimeInMillis(bonusExpire);
						expiration.add(Calendar.SECOND, time); // Cộng thêm thời gian của gói mới (giây)
						long newBonusTime = expiration.getTimeInMillis(); // Mili-giây
						PremiumManager.getInstance().addPremiumTime(player.getObjectId(), time, TimeUnit.SECONDS, id);
						player.setPremiumStatus(true);
						// Gửi thông báo
						String html = HtmCache.getInstance().getHtm(player, "data/html/premium/game.htm");
						if (html == null) {
							player.sendMessage("Error: Could not load the confirmation message.");
							break;
						}
						// Định dạng {time} và {end}
						String formattedTime = formatDuration(time);
						SimpleDateFormat sdf = new SimpleDateFormat("dd.MM.yyyy HH:mm"); // Không cần setTimeZone
						String formattedEnd = sdf.format(expiration.getTime());
						html = html.replace("{time}", formattedTime);
						html = html.replace("{end}", formattedEnd);
						html = replaceVars(player, html);
						player.sendPacket(new NpcHtmlMessage(html));
					}
					// Trường hợp 2: Gói mới cùng level -> Tăng thời gian hết hạn
					else if (currentPremium != null && id == currentPremiumId) {
						int priceId = premium.getPriceId();
						long priceCount = premium.getPriceCount();
						int time = premium.getTime();
						// Kiểm tra và trừ PrimePoints
						if (priceId == -1) // Sử dụng PrimePoints
						{
							if (player.getPrimePoints() < priceCount) {
								player.sendMessage("You do not have enough PrimePoints to extend your Premium package. You need " + priceCount + " PrimePoints.");
								break;
							}
							// Trừ PrimePoints
							player.setPrimePoints((int) (player.getPrimePoints() - priceCount));
						} else // Sử dụng item (hỗ trợ tương thích ngược nếu cần)
						{
							ItemInstance itemInstance = player.getInventory().getItemByItemId(priceId);
							if (itemInstance == null || itemInstance.getCount() < priceCount) {
								player.sendMessage("You do not have enough required items to extend your Premium package.");
								break;
							}
							if (player.getInventory().destroyItemByItemId("PremiumExtend", priceId, priceCount, player, null) == null) {
								player.sendMessage("Failed to deduct the required items. Please try again.");
								break;
							}
						}
						// Tính thời gian hết hạn mới bằng Calendar
						Calendar expiration = Calendar.getInstance();
						expiration.setTimeInMillis(bonusExpire);
						expiration.add(Calendar.SECOND, time);
						long newBonusTime = expiration.getTimeInMillis();
						PremiumManager.getInstance().addPremiumTime(player.getObjectId(), time, TimeUnit.SECONDS, id);
						player.setPremiumStatus(true);
						// Gửi thông báo
						String html = HtmCache.getInstance().getHtm(player, "data/html/premium/game.htm");
						if (html == null) {
							player.sendMessage("Error: Could not load the confirmation message.");
							break;
						}
						// Định dạng {time} và {end}
						String formattedTime = formatDuration(time);
						SimpleDateFormat sdf = new SimpleDateFormat("dd.MM.yyyy HH:mm");
						String formattedEnd = sdf.format(expiration.getTime());
						html = html.replace("{time}", formattedTime);
						html = html.replace("{end}", formattedEnd);
						html = replaceVars(player, html);
						player.sendPacket(new NpcHtmlMessage(html));
					}
					// Trường hợp 3: Gói mới có level thấp hơn -> Chặn
					else {
						player.sendMessage("You already have an active Premium package. You can only upgrade to a higher level package or extend the current one.");
						break;
					}
				} else {
					// Nếu chưa có gói Premium hoặc gói đã hết hạn, mua mới như bình thường
					int priceId = premium.getPriceId();
					long priceCount = premium.getPriceCount();
					int time = premium.getTime();
					// Kiểm tra và trừ PrimePoints
					if (priceId == -1) // Sử dụng PrimePoints
					{
						if (player.getPrimePoints() < priceCount) {
							player.sendMessage("You do not have enough PrimePoints to purchase this Premium package. You need " + priceCount + " PrimePoints.");
							break;
						}
						// Trừ PrimePoints
						player.setPrimePoints((int) (player.getPrimePoints() - priceCount));
					} else // Sử dụng item (hỗ trợ tương thích ngược nếu cần)
					{
						ItemInstance itemInstance = player.getInventory().getItemByItemId(priceId);
						if (itemInstance == null || itemInstance.getCount() < priceCount) {
							player.sendMessage("You do not have enough required items.");
							break;
						}
						if (player.getInventory().destroyItemByItemId("PremiumPurchase", priceId, priceCount, player, null) == null) {
							player.sendMessage("Failed to deduct the required items. Please try again.");
							break;
						}
					}
					// Tính thời gian hết hạn mới bằng Calendar
					Calendar expiration = Calendar.getInstance();
					expiration.setTimeInMillis(current);
					expiration.add(Calendar.SECOND, time);
					long newBonusTime = expiration.getTimeInMillis();
					PremiumManager.getInstance().addPremiumTime(player.getObjectId(), time, TimeUnit.SECONDS, id);
					player.setPremiumStatus(true);
					// Gửi thông báo
					String html = HtmCache.getInstance().getHtm(player, "data/html/premium/game.htm");
					if (html == null) {
						player.sendMessage("Error: Could not load the confirmation message.");
						break;
					}
					// Định dạng {time}, {end}, và {premium_name}
					String formattedTime = formatDuration(time);
					SimpleDateFormat sdf = new SimpleDateFormat("dd.MM.yyyy HH:mm");
					String formattedEnd = sdf.format(expiration.getTime());
					String premiumName = PremiumManager.getInstance().getPremiumName(player.getObjectId());
					html = html.replace("{time}", formattedTime);
					html = html.replace("{end}", formattedEnd);
					html = html.replace("{premium_name}", premiumName);
					html = replaceVars(player, html);
					player.sendPacket(new NpcHtmlMessage(html));
				}
				break;
			}
			case "_bbspremiumgiftinfo": {
				int id = Integer.parseInt(parts[1]);
				String html = HtmCache.getInstance().getHtm(player, "data/html/premium/gift_info.htm");
				PremiumAccount premium = PremiumData.getInstance().getPremium(id);
				if (html == null) {
					player.sendMessage("Error: Could not load premium gift info page.");
					break;
				}
				if (premium == null) {
					player.sendMessage("Premium package not found.");
					break;
				}
				// Thay thế các placeholder cơ bản
				html = html.replace("{name}", premium.getName());
				html = html.replace("{time}", Util.formatTime(premium.getTime()));
				html = html.replace("{icon}", premium.getIcon());
				html = html.replace("{id}", String.valueOf(id));
				// Tạo danh sách quà tặng
				StringBuilder giftListHtml = new StringBuilder();
				List<PremiumGift> gifts = premium.getGifts();
				if (gifts != null && !gifts.isEmpty()) {
					for (PremiumGift gift : gifts) {
						Item item = ItemTable.getInstance().getTemplate(gift.getId());
						String itemName = (item != null) ? item.getName() : "Unknown Item (ID: " + gift.getId() + ")";
						giftListHtml.append("<tr>");
						giftListHtml.append("<td align=center>");
						giftListHtml.append("<font color=99CC66>").append(itemName).append(":</font> x").append(gift.getCount());
						giftListHtml.append("</td>");
						giftListHtml.append("</tr>");
					}
				} else {
					giftListHtml.append("<tr>");
					giftListHtml.append("<td align=center>");
					giftListHtml.append("<font color=FF0000>No gifts available for this package.</font>");
					giftListHtml.append("</td>");
					giftListHtml.append("</tr>");
				}
				html = html.replace("{gift_list}", giftListHtml.toString());
				html = replaceVars(player, html);
				player.sendPacket(new NpcHtmlMessage(html));
				break;
			}
			case "_bbstoggle_share_premium": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/menu/player.htm");

				// Check if player has individual premium
				if (!player.hasPremiumStatus()) {
					player.sendMessage("You need individual premium to share with party members!");
					if (dialog != null) {
						dialog = replaceVars(player, dialog);
						player.sendPacket(new NpcHtmlMessage(dialog));
					}
					break;
				}

				// Toggle share premium setting (similar to town mode)
				final boolean oldValue = player.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false);
				player.getVariables().set("SHARE_PREMIUM_TO_PARTY", !oldValue);

				if (!oldValue) // Now enabled
				{
					// Set default share rate if not set
					if (player.getVariables().getInt("PREMIUM_SHARE_RATE", 0) == 0) {
						player.getVariables().set("PREMIUM_SHARE_RATE", 50); // Default 50%
					}

					// Force reset sharing start time when enabling
					long currentTime = System.currentTimeMillis();
					player.getVariables().set("PREMIUM_SHARING_START_TIME", currentTime);
					player.getVariables().storeMe();

					if (player.getVariables().getBoolean("DEBUG", false)) {
						System.out.println("DEBUG MainCBH: Reset sharing start time to " + currentTime);
					}

					if (player.getParty() != null && player.getParty().getMemberCount() > 1) {
						PremiumManager.getInstance().applySharedPremiumToParty(player);
						// Get the actual best rate that will be applied
						int bestRate = getBestShareRateInParty(player.getParty());
						player.sendMessage("Premium sharing enabled! Party members will receive " + bestRate + "% benefits (best rate in party).");
					} else {
						player.sendMessage("Premium sharing enabled! Benefits will be shared when you join/create a party.");
					}
				} else // Now disabled
				{
					PremiumManager.getInstance().removeSharedPremiumFromParty(player);
					player.sendMessage("Premium sharing disabled. Premium time consumed based on sharing duration.");
				}

				if (dialog != null) {
					dialog = replaceVars(player, dialog);
					player.sendPacket(new NpcHtmlMessage(dialog));
				}
				break;
			}
			case "_bbstoggle_auto_resurrection": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/menu/player.htm");

				// Check if auto-resurrection feature is enabled
				if (!club.projectessence.Config.AUTOPLAY_AUTO_RESURRECTION_ENABLED) {
					player.sendMessage("Auto-resurrection feature is disabled on this server.");
					if (dialog != null) {
						dialog = replaceVars(player, dialog);
						player.sendPacket(new NpcHtmlMessage(dialog));
					}
					break;
				}

				// Toggle auto-resurrection setting
				final boolean oldValue = player.getAutoPlaySettings().isAutoResurrectionEnabled(player);
				club.projectessence.gameserver.taskmanager.autoplay.AutoPlayTaskManager.getInstance().setAutoResurrection(player, !oldValue);

				if (dialog != null) {
					dialog = replaceVars(player, dialog);
					player.sendPacket(new NpcHtmlMessage(dialog));
				}
				break;
			}
			case "_bbsupdate_share_rate": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/menu/player.htm");

				// Check if player has individual premium
				if (!player.hasPremiumStatus()) {
					player.sendMessage("You need individual premium to set share rate!");
					if (dialog != null) {
						dialog = replaceVars(player, dialog);
						player.sendPacket(new NpcHtmlMessage(dialog));
					}
					break;
				}

				// Parse share rate from command
				try {
					String[] rateParts = command.split(" ");
					if (rateParts.length < 2) {
						player.sendMessage("Usage: Share rate must be between 10-90%");
						break;
					}

					int newShareRate = Integer.parseInt(rateParts[1]);

					// Validate range (10-90%)
					if (newShareRate < 10 || newShareRate > 90) {
						player.sendMessage("Share rate must be between 10-90%");
						break;
					}

					// Update share rate
					player.getVariables().set("PREMIUM_SHARE_RATE", newShareRate);
					player.getVariables().storeMe();

					// If currently sharing, reapply with new rate
					if (player.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false) &&
							player.getParty() != null && player.getParty().getMemberCount() > 1) {
						PremiumManager.getInstance().reapplySharedPremiumToParty(player);
						// Get the actual best rate that will be applied
						int bestRate = getBestShareRateInParty(player.getParty());
						if (bestRate == newShareRate) {
							player.sendMessage("Share rate updated to " + newShareRate + "%. You're providing the best rate in party!");
						} else {
							player.sendMessage("Share rate updated to " + newShareRate + "%. Party receives " + bestRate + "% (best rate from another player).");
						}
					} else {
						player.sendMessage("Share rate updated to " + newShareRate + "%");
					}
				} catch (NumberFormatException e) {
					player.sendMessage("Invalid share rate. Please enter a number between 10-90.");
				}

				// Toggle share premium setting (similar to town mode)
				final boolean oldValue = player.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false);
				player.getVariables().set("SHARE_PREMIUM_TO_PARTY", !oldValue);

				if (!oldValue) // Now enabled
				{
					// Set default share rate if not set
					if (player.getVariables().getInt("PREMIUM_SHARE_RATE", 0) == 0) {
						player.getVariables().set("PREMIUM_SHARE_RATE", 50); // Default 50%
					}

					if (player.getParty() != null && player.getParty().getMemberCount() > 1) {
						PremiumManager.getInstance().applySharedPremiumToParty(player);
						// Get the actual best rate that will be applied
						int bestRate = getBestShareRateInParty(player.getParty());
						player.sendMessage("Premium sharing enabled! Party members will receive " + bestRate + "% benefits (best rate in party).");
					} else {
						player.sendMessage("Premium sharing enabled! Benefits will be shared when you join/create a party.");
					}
				} else // Now disabled
				{
					PremiumManager.getInstance().removeSharedPremiumFromParty(player);
					player.sendMessage("Premium sharing disabled. Premium time consumed based on sharing duration.");
				}

				if (dialog != null) {
					dialog = replaceVars(player, dialog);
					player.sendPacket(new NpcHtmlMessage(dialog));
				}
				break;
			}
			case "_checksharepremium": {
				try {
					String html = HtmCache.getInstance().getHtm(player, "data/html/premium/shared_premium_info.htm");
					if (html == null) {
						player.sendMessage("Error: Shared premium info template not found!");
						return false;
					}

					// Check if player has shared premium
					boolean hasSharedPremium = player.getVariables().getFloat("SHARED_PREMIUM_EXP_RATE", 0.0f) > 0.0f;
					boolean hasIndividualPremium = player.hasPremiumStatus();

					String statusTitle, status, source;

					if (hasSharedPremium) {
						statusTitle = "Receiving Shared Premium Benefits";
						status = "<font color=00FF00>Active</font>";

						// Find who is sharing premium in party
						String sharingPlayer = "Unknown";
						if (player.getParty() != null) {
							for (PlayerInstance member : player.getParty().getMembers()) {
								if (member != null && member != player && member.hasPremiumStatus() &&
										member.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false)) {
									sharingPlayer = member.getName();
									break;
								}
							}
						}
						source = sharingPlayer;
					} else if (hasIndividualPremium) {
						statusTitle = "Individual Premium Account";
						status = "<font color=FFFF00>Individual Premium</font>";
						source = "Personal Premium Package";
					} else {
						statusTitle = "No Premium Benefits";
						status = "<font color=FF0000>Inactive</font>";
						source = "None";
					}

					// Replace status info
					html = html.replace("{shared_status_title}", statusTitle);
					html = html.replace("{shared_status}", status);
					html = html.replace("{shared_source}", source);

					// Get rates (shared or individual)
					float expRate = hasSharedPremium ? player.getVariables().getFloat("SHARED_PREMIUM_EXP_RATE", 1.0f) :
							(hasIndividualPremium ? player.getVariables().getFloat(PlayerVariables.PREMIUM_EXP_RATE, 1.0f) : 1.0f);
					float spRate = hasSharedPremium ? player.getVariables().getFloat("SHARED_PREMIUM_SP_RATE", 1.0f) :
							(hasIndividualPremium ? player.getVariables().getFloat(PlayerVariables.PREMIUM_SP_RATE, 1.0f) : 1.0f);
					float adenaChance = hasSharedPremium ? player.getVariables().getFloat("SHARED_PREMIUM_ADENA_CHANCE", 1.0f) :
							(hasIndividualPremium ? player.getVariables().getFloat(PlayerVariables.PREMIUM_ADENA_CHANCE, 1.0f) : 1.0f);
					float adenaAmount = hasSharedPremium ? player.getVariables().getFloat("SHARED_PREMIUM_ADENA_AMOUNT", 1.0f) :
							(hasIndividualPremium ? player.getVariables().getFloat(PlayerVariables.PREMIUM_ADENA_AMOUNT, 1.0f) : 1.0f);
					float dropChance = hasSharedPremium ? player.getVariables().getFloat("SHARED_PREMIUM_DROP_CHANCE", 1.0f) :
							(hasIndividualPremium ? player.getVariables().getFloat(PlayerVariables.PREMIUM_DROP_CHANCE, 1.0f) : 1.0f);
					float dropAmount = hasSharedPremium ? player.getVariables().getFloat("SHARED_PREMIUM_DROP_AMOUNT", 1.0f) :
							(hasIndividualPremium ? player.getVariables().getFloat(PlayerVariables.PREMIUM_DROP_AMOUNT, 1.0f) : 1.0f);
					float spoilChance = hasSharedPremium ? player.getVariables().getFloat("SHARED_PREMIUM_SPOIL_CHANCE", 1.0f) :
							(hasIndividualPremium ? player.getVariables().getFloat(PlayerVariables.PREMIUM_SPOIL_CHANCE, 1.0f) : 1.0f);
					float spoilAmount = hasSharedPremium ? player.getVariables().getFloat("SHARED_PREMIUM_SPOIL_AMOUNT", 1.0f) :
							(hasIndividualPremium ? player.getVariables().getFloat(PlayerVariables.PREMIUM_SPOIL_AMOUNT, 1.0f) : 1.0f);
					float sayhaGrace = hasSharedPremium ? player.getVariables().getFloat("SHARED_PREMIUM_SAYHA_GRACE_XP", 1.0f) :
							(hasIndividualPremium ? player.getVariables().getFloat(PlayerVariables.PREMIUM_SAYHA_GRACE_XP, 1.0f) : 1.0f);
					float limitedSayhaGrace = hasSharedPremium ? player.getVariables().getFloat("SHARED_PREMIUM_LIMITED_SAYHA_GRACE_XP", 1.0f) :
							(hasIndividualPremium ? player.getVariables().getFloat(PlayerVariables.PREMIUM_LIMITED_SAYHA_GRACE_XP, 1.0f) : 1.0f);
					float resurrectionCost = hasSharedPremium ? player.getVariables().getFloat("SHARED_PREMIUM_RESURRECTION_COST", 1.0f) :
							(hasIndividualPremium ? player.getVariables().getFloat(PlayerVariables.PREMIUM_RESURRECTION_COST, 1.0f) : 1.0f);
					float randomCraftHerb = hasSharedPremium ? player.getVariables().getFloat("SHARED_PREMIUM_RANDOM_CRAFT_HERB", 1.0f) :
							(hasIndividualPremium ? player.getVariables().getFloat(PlayerVariables.PREMIUM_RANDOM_CRAFT_HERB, 1.0f) : 1.0f);
					float lcoinBonus = hasSharedPremium ? player.getVariables().getFloat("SHARED_PREMIUM_LCOIN_DROP_AMOUNT_ADD", 0.0f) :
							(hasIndividualPremium ? player.getVariables().getFloat(PlayerVariables.PREMIUM_LCOIN_DROP_AMOUNT_ADD, 0.0f) : 0.0f);

					// Format rates as percentages
					html = html.replace("{exp_rate}", formatRateAsPercentage(expRate));
					html = html.replace("{sp_rate}", formatRateAsPercentage(spRate));
					html = html.replace("{adena_chance}", formatRateAsPercentage(adenaChance));
					html = html.replace("{adena_amount}", formatRateAsPercentage(adenaAmount));
					html = html.replace("{drop_chance}", formatRateAsPercentage(dropChance));
					html = html.replace("{drop_amount}", formatRateAsPercentage(dropAmount));
					html = html.replace("{spoil_chance}", formatRateAsPercentage(spoilChance));
					html = html.replace("{spoil_amount}", formatRateAsPercentage(spoilAmount));
					html = html.replace("{sayha_grace}", formatRateAsPercentage(sayhaGrace));
					html = html.replace("{limited_sayha_grace}", formatRateAsPercentage(limitedSayhaGrace));
					html = html.replace("{resurrection_cost}", formatRateAsPercentage(resurrectionCost));
					html = html.replace("{random_craft_herb}", formatRateAsPercentage(randomCraftHerb));
					html = html.replace("{lcoin_bonus}", lcoinBonus > 0 ? String.format("+%.0f%%", lcoinBonus * 100) : "+0%");

					player.sendPacket(new NpcHtmlMessage(html));
				} catch (Exception e) {
					player.sendMessage("Error loading shared premium info: " + e.getMessage());
					e.printStackTrace();
				}
				break;
			}
			case "_bbsgveskill": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/gve/gve_skill_menu.html");
				if (dialog != null) {
					Map<Long, SkillLearn> skillMap = SkillTreeData.getInstance().getGveSkillTree(player.getClassId(), 1); // 1 sao
					StringBuilder skillListHtml = new StringBuilder();
					int skillsPerPage = 15;
					int currentPage = 1;
					if (parts.length > 1) {
						try {
							currentPage = Integer.parseInt(parts[1]);
						} catch (NumberFormatException e) {
							currentPage = 1;
						}
					}
					currentPage = Math.max(1, Math.min(currentPage, (int) Math.ceil((double) (skillMap != null ? skillMap.size() : 0) / skillsPerPage)));
					if (skillMap != null && !skillMap.isEmpty()) {
						List<SkillLearn> skillList = new ArrayList<>(skillMap.values());
						int totalSkills = skillList.size();
						int totalPages = (int) Math.ceil((double) totalSkills / skillsPerPage);
						int startIndex = (currentPage - 1) * skillsPerPage;
						int endIndex = Math.min(startIndex + skillsPerPage, totalSkills);
						for (int i = startIndex; i < endIndex; i++) {
							SkillLearn skillLearn = skillList.get(i);
							Skill skill = SkillData.getInstance().getSkill(skillLearn.getSkillId(), skillLearn.getSkillLevel());
							if (skill != null) {
								Skill knownSkill = player.getKnownSkill(skill.getId());
								if (knownSkill != null && knownSkill.getLevel() >= skill.getLevel()) {
									continue;
								}
								String bgcolor = (i % 2 == 0) ? "L2UI_CT1.LCoinShopWnd_DF_Button_Down" : "L2UI_CT1.LCoinShopWnd_DF_Button";
								int currentSP = (player.getGveSkillPoints() != 0) ? player.getGveSkillPoints() : 0;
								int requiredSP = (skillLearn.getGveSkillPointsCost() != 0) ? skillLearn.getGveSkillPointsCost() : 0;
								String spInfo = String.format("(<font color=LEVEL>%d/%d</font>)", currentSP, requiredSP);
								skillListHtml.append("<tr>").append("<td align=center>").append("<table border=0 cellpadding=0 cellspacing=0 width=290 height=40 background=\"").append(bgcolor).append("\">").append("<tr>").append("<td width=32 align=center valign=middle><img src=\"").append(skill.getIcon()).append("\" width=32 height=32></td>").append("<td width=100 align=left valign=middle>").append(skill.getName()).append(" (Lv ").append(skill.getLevel()).append(") ").append(spInfo).append("</td>").append("<td width=50 align=right valign=middle><button value=\" \" action=\"bypass _bbsgveskilllearn_").append(skillLearn.getSkillId()).append("_").append(skillLearn.getSkillLevel()).append("_1\" width=32 height=32 back=\"L2UI_CT1.MiniMap_DF_PlusBtn_Gray_Down\" fore=\"L2UI_CT1.MiniMap_DF_PlusBtn_Gray\"/></td>").append("</tr>").append("</table>").append("</td>").append("</tr>");
							}
						}
						skillListHtml.append("<tr><td align=center><br>");
						if (currentPage > 1) {
							skillListHtml.append("<button value=\"Prev\" action=\"bypass _bbsgveskill ").append(currentPage - 1).append("\" width=60 height=25 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
						}
						skillListHtml.append(" Page ").append(currentPage).append(" of ").append(totalPages);
						if (currentPage < totalPages) {
							skillListHtml.append("<button value=\"Next\" action=\"bypass _bbsgveskill ").append(currentPage + 1).append("\" width=60 height=25 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
						}
						skillListHtml.append("</td></tr>");
					} else {
						skillListHtml.append("<tr><td align=center><font color=FF0000>No GvE skills available for 1* level.</font></td></tr>");
					}
					dialog = dialog.replace("%skill_list%", skillListHtml.toString());
					dialog = dialog.replace("%gve_skill_points%", String.valueOf(player.getGveSkillPoints()));
					dialog = dialog.replace("%daily_points%", String.valueOf(player.getDailyGveSkillPointsForDisplay()));
					dialog = dialog.replace("%daily_limit%", String.valueOf(player.getGveSkillDailyLimit()));
					dialog = replaceVars(player, dialog);
					player.sendPacket(new ExPremiumManagerShowHtml(dialog));
				}
				break;
			}
			case "_bbsgveskill_1": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/gve/gve_skill_menu_1.html");
				if (dialog != null) {
					Map<Long, SkillLearn> skillMap = SkillTreeData.getInstance().getGveSkillTree(player.getClassId(), 2); // 2 sao
					StringBuilder skillListHtml = new StringBuilder();
					int skillsPerPage = 15;
					int currentPage = 1;
					if (parts.length > 1) {
						try {
							currentPage = Integer.parseInt(parts[1]);
						} catch (NumberFormatException e) {
							currentPage = 1;
						}
					}
					currentPage = Math.max(1, Math.min(currentPage, (int) Math.ceil((double) (skillMap != null ? skillMap.size() : 0) / skillsPerPage)));
					if (skillMap != null && !skillMap.isEmpty()) {
						List<SkillLearn> skillList = new ArrayList<>(skillMap.values());
						int totalSkills = skillList.size();
						int totalPages = (int) Math.ceil((double) totalSkills / skillsPerPage);
						int startIndex = (currentPage - 1) * skillsPerPage;
						int endIndex = Math.min(startIndex + skillsPerPage, totalSkills);
						for (int i = startIndex; i < endIndex; i++) {
							SkillLearn skillLearn = skillList.get(i);
							Skill skill = SkillData.getInstance().getSkill(skillLearn.getSkillId(), skillLearn.getSkillLevel());
							if (skill != null) {
								Skill knownSkill = player.getKnownSkill(skill.getId());
								if (knownSkill != null && knownSkill.getLevel() >= skill.getLevel()) {
									continue;
								}
								String bgcolor = (i % 2 == 0) ? "L2UI_CT1.LCoinShopWnd_DF_Button_Down" : "L2UI_CT1.LCoinShopWnd_DF_Button";
								int currentSP = (player.getGveSkillPoints() != 0) ? player.getGveSkillPoints() : 0;
								int requiredSP = (skillLearn.getGveSkillPointsCost() != 0) ? skillLearn.getGveSkillPointsCost() : 0;
								String spInfo = String.format("(<font color=LEVEL>%d/%d</font>)", currentSP, requiredSP);
								skillListHtml.append("<tr>").append("<td align=center>").append("<table border=0 cellpadding=0 cellspacing=0 width=310 height=40 background=\"").append(bgcolor).append("\">").append("<tr>").append("<td width=32 align=center valign=middle><img src=\"").append(skill.getIcon()).append("\" width=32 height=32></td>").append("<td width=100 align=left valign=middle>").append(skill.getName()).append(" (Lv ").append(skill.getLevel()).append(") ").append(spInfo).append("</td>").append("<td width=50 align=right valign=middle><button value=\" \" action=\"bypass _bbsgveskilllearn_").append(skillLearn.getSkillId()).append("_").append(skillLearn.getSkillLevel()).append("_2\" width=32 height=32 back=\"L2UI_CT1.MiniMap_DF_PlusBtn_Gray_Down\" fore=\"L2UI_CT1.MiniMap_DF_PlusBtn_Gray\"/></td>").append("</tr>").append("</table>").append("</td>").append("</tr>");
							}
						}
						skillListHtml.append("<tr><td align=center><br>");
						if (currentPage > 1) {
							skillListHtml.append("<button value=\"Prev\" action=\"bypass _bbsgveskill_1 ").append(currentPage - 1).append("\" width=60 height=25 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
						}
						skillListHtml.append(" Page ").append(currentPage).append(" of ").append(totalPages);
						if (currentPage < totalPages) {
							skillListHtml.append("<button value=\"Next\" action=\"bypass _bbsgveskill_1 ").append(currentPage + 1).append("\" width=60 height=25 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
						}
						skillListHtml.append("</td></tr>");
					} else {
						skillListHtml.append("<tr><td align=center><font color=FF0000>No GvE skills available for 2* level.</font></td></tr>");
					}
					dialog = dialog.replace("%skill_list%", skillListHtml.toString());
					dialog = dialog.replace("%gve_skill_points%", String.valueOf(player.getGveSkillPoints()));
					dialog = dialog.replace("%daily_points%", String.valueOf(player.getDailyGveSkillPointsForDisplay()));
					dialog = dialog.replace("%daily_limit%", String.valueOf(player.getGveSkillDailyLimit()));
					dialog = replaceVars(player, dialog);
					player.sendPacket(new ExPremiumManagerShowHtml(dialog));
				}
				break;
			}
			case "_bbsgveskill_2": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/gve/gve_skill_menu_2.html");
				if (dialog != null) {
					Map<Long, SkillLearn> skillMap = SkillTreeData.getInstance().getGveSkillTree(player.getClassId(), 3); // 3 sao
					StringBuilder skillListHtml = new StringBuilder();
					int skillsPerPage = 15;
					int currentPage = 1;
					if (parts.length > 1) {
						try {
							currentPage = Integer.parseInt(parts[1]);
						} catch (NumberFormatException e) {
							currentPage = 1;
						}
					}
					currentPage = Math.max(1, Math.min(currentPage, (int) Math.ceil((double) (skillMap != null ? skillMap.size() : 0) / skillsPerPage)));
					if (skillMap != null && !skillMap.isEmpty()) {
						List<SkillLearn> skillList = new ArrayList<>(skillMap.values());
						int totalSkills = skillList.size();
						int totalPages = (int) Math.ceil((double) totalSkills / skillsPerPage);
						int startIndex = (currentPage - 1) * skillsPerPage;
						int endIndex = Math.min(startIndex + skillsPerPage, totalSkills);
						for (int i = startIndex; i < endIndex; i++) {
							SkillLearn skillLearn = skillList.get(i);
							Skill skill = SkillData.getInstance().getSkill(skillLearn.getSkillId(), skillLearn.getSkillLevel());
							if (skill != null) {
								Skill knownSkill = player.getKnownSkill(skill.getId());
								if (knownSkill != null && knownSkill.getLevel() >= skill.getLevel()) {
									continue;
								}
								String bgcolor = (i % 2 == 0) ? "L2UI_CT1.LCoinShopWnd_DF_Button_Down" : "L2UI_CT1.LCoinShopWnd_DF_Button";
								int currentSP = (player.getGveSkillPoints() != 0) ? player.getGveSkillPoints() : 0;
								int requiredSP = (skillLearn.getGveSkillPointsCost() != 0) ? skillLearn.getGveSkillPointsCost() : 0;
								String spInfo = String.format("(<font color=LEVEL>%d/%d</font>)", currentSP, requiredSP);
								skillListHtml.append("<tr>").append("<td align=center>").append("<table border=0 cellpadding=0 cellspacing=0 width=310 height=40 background=\"").append(bgcolor).append("\">").append("<tr>").append("<td width=32 align=center valign=middle><img src=\"").append(skill.getIcon()).append("\" width=32 height=32></td>").append("<td width=100 align=left valign=middle>").append(skill.getName()).append(" (Lv ").append(skill.getLevel()).append(") ").append(spInfo).append("</td>").append("<td width=50 align=right valign=middle><button value=\" \" action=\"bypass _bbsgveskilllearn_").append(skillLearn.getSkillId()).append("_").append(skillLearn.getSkillLevel()).append("_3\" width=32 height=32 back=\"L2UI_CT1.MiniMap_DF_PlusBtn_Gray_Down\" fore=\"L2UI_CT1.MiniMap_DF_PlusBtn_Gray\"/></td>").append("</tr>").append("</table>").append("</td>").append("</tr>");
							}
						}
						skillListHtml.append("<tr><td align=center><br>");
						if (currentPage > 1) {
							skillListHtml.append("<button value=\"Prev\" action=\"bypass _bbsgveskill_2 ").append(currentPage - 1).append("\" width=60 height=25 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
						}
						skillListHtml.append(" Page ").append(currentPage).append(" of ").append(totalPages);
						if (currentPage < totalPages) {
							skillListHtml.append("<button value=\"Next\" action=\"bypass _bbsgveskill_2 ").append(currentPage + 1).append("\" width=60 height=25 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
						}
						skillListHtml.append("</td></tr>");
					} else {
						skillListHtml.append("<tr><td align=center><font color=FF0000>No GvE skills available for 3* level.</font></td></tr>");
					}
					dialog = dialog.replace("%skill_list%", skillListHtml.toString());
					dialog = dialog.replace("%gve_skill_points%", String.valueOf(player.getGveSkillPoints()));
					dialog = dialog.replace("%daily_points%", String.valueOf(player.getDailyGveSkillPointsForDisplay()));
					dialog = dialog.replace("%daily_limit%", String.valueOf(player.getGveSkillDailyLimit()));
					dialog = replaceVars(player, dialog);
					player.sendPacket(new ExPremiumManagerShowHtml(dialog));
				}
				break;
			}
			case "_bbsgveskill_3": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/gve/gve_skill_menu_3.html");
				if (dialog != null) {
					Map<Long, SkillLearn> skillMap = SkillTreeData.getInstance().getGveSkillTree(player.getClassId(), 4); // 4 sao
					StringBuilder skillListHtml = new StringBuilder();
					int skillsPerPage = 15;
					int currentPage = 1;
					if (parts.length > 1) {
						try {
							currentPage = Integer.parseInt(parts[1]);
						} catch (NumberFormatException e) {
							currentPage = 1;
						}
					}
					currentPage = Math.max(1, Math.min(currentPage, (int) Math.ceil((double) (skillMap != null ? skillMap.size() : 0) / skillsPerPage)));
					if (skillMap != null && !skillMap.isEmpty()) {
						List<SkillLearn> skillList = new ArrayList<>(skillMap.values());
						int totalSkills = skillList.size();
						int totalPages = (int) Math.ceil((double) totalSkills / skillsPerPage);
						int startIndex = (currentPage - 1) * skillsPerPage;
						int endIndex = Math.min(startIndex + skillsPerPage, totalSkills);
						for (int i = startIndex; i < endIndex; i++) {
							SkillLearn skillLearn = skillList.get(i);
							Skill skill = SkillData.getInstance().getSkill(skillLearn.getSkillId(), skillLearn.getSkillLevel());
							if (skill != null) {
								Skill knownSkill = player.getKnownSkill(skill.getId());
								if (knownSkill != null && knownSkill.getLevel() >= skill.getLevel()) {
									continue;
								}
								String bgcolor = (i % 2 == 0) ? "L2UI_CT1.LCoinShopWnd_DF_Button_Down" : "L2UI_CT1.LCoinShopWnd_DF_Button";
								int currentSP = (player.getGveSkillPoints() != 0) ? player.getGveSkillPoints() : 0;
								int requiredSP = (skillLearn.getGveSkillPointsCost() != 0) ? skillLearn.getGveSkillPointsCost() : 0;
								String spInfo = String.format("(<font color=LEVEL>%d/%d</font>)", currentSP, requiredSP);
								skillListHtml.append("<tr>").append("<td align=center>").append("<table border=0 cellpadding=0 cellspacing=0 width=310 height=40 background=\"").append(bgcolor).append("\">").append("<tr>").append("<td width=32 align=center valign=middle><img src=\"").append(skill.getIcon()).append("\" width=32 height=32></td>").append("<td width=100 align=left valign=middle>").append(skill.getName()).append(" (Lv ").append(skill.getLevel()).append(") ").append(spInfo).append("</td>").append("<td width=50 align=right valign=middle><button value=\" \" action=\"bypass _bbsgveskilllearn_").append(skillLearn.getSkillId()).append("_").append(skillLearn.getSkillLevel()).append("_4\" width=32 height=32 back=\"L2UI_CT1.MiniMap_DF_PlusBtn_Gray_Down\" fore=\"L2UI_CT1.MiniMap_DF_PlusBtn_Gray\"/></td>").append("</tr>").append("</table>").append("</td>").append("</tr>");
							}
						}
						skillListHtml.append("<tr><td align=center><br>");
						if (currentPage > 1) {
							skillListHtml.append("<button value=\"Prev\" action=\"bypass _bbsgveskill_3 ").append(currentPage - 1).append("\" width=60 height=25 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
						}
						skillListHtml.append(" Page ").append(currentPage).append(" of ").append(totalPages);
						if (currentPage < totalPages) {
							skillListHtml.append("<button value=\"Next\" action=\"bypass _bbsgveskill_3 ").append(currentPage + 1).append("\" width=60 height=25 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
						}
						skillListHtml.append("</td></tr>");
					} else {
						skillListHtml.append("<tr><td align=center><font color=FF0000>No GvE skills available for 4* level.</font></td></tr>");
					}
					dialog = dialog.replace("%skill_list%", skillListHtml.toString());
					dialog = dialog.replace("%gve_skill_points%", String.valueOf(player.getGveSkillPoints()));
					dialog = dialog.replace("%daily_points%", String.valueOf(player.getDailyGveSkillPointsForDisplay()));
					dialog = dialog.replace("%daily_limit%", String.valueOf(player.getGveSkillDailyLimit()));
					dialog = replaceVars(player, dialog);
					player.sendPacket(new ExPremiumManagerShowHtml(dialog));
				}
				break;
			}
			case "_bbsgve_skill_points_info": {
				String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/gve/gve_skill_points_info.html");
				if (dialog != null) {
					dialog = replaceVars(player, dialog);
					player.sendPacket(new ExPremiumManagerShowHtml(dialog));
				} else {
					player.sendMessage("Error: Could not load GvE Skill Points info page.");
				}
				break;
			}
			case "_bbsgiftcode": {
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/giftcode.html");
				returnHtml = replaceGiftcodeVars(player, returnHtml);
				CommunityBoardHandler.separateAndSend(returnHtml, player);
				break;
			}
			case "_bbsgiftcode_use": {
				if (parts.length < 2) {
					player.sendMessage("Vui lòng nhập giftcode.");
					return false;
				}

				String giftcode = parts[1].trim();
				if (giftcode.isEmpty()) {
					player.sendMessage("Giftcode không được để trống.");
					return false;
				}

				// Use giftcode through manager
				club.projectessence.gameserver.instancemanager.GiftcodeManager.GiftcodeResult result =
					club.projectessence.gameserver.instancemanager.GiftcodeManager.getInstance().useGiftcode(player, giftcode);

				// Send result message
				player.sendMessage(result.getMessage());

				// Refresh giftcode page
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/giftcode.html");
				returnHtml = replaceGiftcodeVars(player, returnHtml);
				CommunityBoardHandler.separateAndSend(returnHtml, player);
				break;
			}
			default: {
				if (command.toLowerCase().startsWith("_bbsgveskilllearn_")) {
					String[] tokens = command.split("_");
					baseCommand = "";
					int tokenIndex = 0;
					while (tokenIndex < tokens.length) {
						if (!tokens[tokenIndex].isEmpty()) {
							baseCommand += "_" + tokens[tokenIndex].toLowerCase();
							if (baseCommand.equals("_bbsgveskilllearn")) {
								tokenIndex++;
								break;
							}
						}
						tokenIndex++;
					}
					if (baseCommand.equals("_bbsgveskilllearn") && tokenIndex + 2 < tokens.length) // Yêu cầu skillId, skillLevel, và starLevel
					{
						try {
							int skillId = Integer.parseInt(tokens[tokenIndex]);
							int skillLevel = Integer.parseInt(tokens[tokenIndex + 1]);
							int starLevel = Integer.parseInt(tokens[tokenIndex + 2]); // Lấy starLevel từ lệnh
							ClassId classId = player.getClassId();
							SkillLearn skillLearn = SkillTreeData.getInstance().getGveSkillTree(classId, starLevel).get(SkillData.getSkillHashCode(skillId, skillLevel));
							if (skillLearn != null) {
								if (player.getGveSkillPoints() >= skillLearn.getGveSkillPointsCost()) {
									if (player.useGveSkillPoints(skillLearn.getGveSkillPointsCost())) {
										Skill skill = SkillData.getInstance().getSkill(skillId, skillLevel);
										if (skill != null) {
											player.addSkill(skill, true);
											player.sendPacket(new SystemMessage(SystemMessageId.YOU_HAVE_LEARNED_S1).addSkillName(skillId));
											player.sendSkillList();

											// Add museum data for GVE skill learned
											MuseumManager.getInstance().addGveSkillsLearned(player);
											// Tải lại giao diện gve_skill_menu.html với dữ liệu mới, dựa trên starLevel
											String dialogFile = "";
											switch (starLevel) {
												case 1:
													dialogFile = "data/html/CommunityBoard/KhoaCustom/gve/gve_skill_menu.html";
													break;
												case 2:
													dialogFile = "data/html/CommunityBoard/KhoaCustom/gve/gve_skill_menu_1.html";
													break;
												case 3:
													dialogFile = "data/html/CommunityBoard/KhoaCustom/gve/gve_skill_menu_2.html";
													break;
												case 4:
													dialogFile = "data/html/CommunityBoard/KhoaCustom/gve/gve_skill_menu_3.html";
													break;
												default:
													dialogFile = "data/html/CommunityBoard/KhoaCustom/gve/gve_skill_menu.html"; // Default case
													break;
											}
											String dialog = HtmCache.getInstance().getHtm(player, dialogFile);
											if (dialog != null) {
												ClassId updatedClassId = player.getClassId();
												Map<Long, SkillLearn> updatedGveSkills = SkillTreeData.getInstance().getGveSkillTree(updatedClassId, starLevel);
												StringBuilder updatedSkillListHtml = new StringBuilder();
												int updatedSkillsPerPage = 15;
												int updatedCurrentPage = 1;
												if (parts.length > 3) // Kiểm tra nếu có trang số
												{
													try {
														updatedCurrentPage = Integer.parseInt(parts[3]);
													} catch (NumberFormatException e) {
														updatedCurrentPage = 1;
													}
												}
												int updatedTotalPages = (int) Math.ceil((double) (updatedGveSkills != null ? updatedGveSkills.size() : 0) / updatedSkillsPerPage);
												int updatedStartIndex = (updatedCurrentPage - 1) * updatedSkillsPerPage;
												int updatedEndIndex = Math.min(updatedStartIndex + updatedSkillsPerPage, updatedGveSkills != null ? updatedGveSkills.size() : 0);
												if (updatedGveSkills != null && !updatedGveSkills.isEmpty()) {
													List<SkillLearn> updatedSkillList = new ArrayList<>(updatedGveSkills.values());
													for (int i = updatedStartIndex; i < updatedEndIndex; i++) {
														SkillLearn updatedSkillLearn = updatedSkillList.get(i);
														Skill updatedSkill = SkillData.getInstance().getSkill(updatedSkillLearn.getSkillId(), updatedSkillLearn.getSkillLevel());
														if (updatedSkill != null) {
															Skill knownSkill = player.getKnownSkill(updatedSkill.getId());
															if (knownSkill != null && knownSkill.getLevel() >= updatedSkill.getLevel()) {
																continue;
															}
															String bgcolor = (i % 2 == 0) ? "L2UI_CT1.LCoinShopWnd_DF_Button_Down" : "L2UI_CT1.LCoinShopWnd_DF_Button";
															int currentSP = player.getGveSkillPoints();
															int requiredSP = updatedSkillLearn.getGveSkillPointsCost();
															String spInfo = String.format("(<font color=LEVEL>%d/%d</font>)", currentSP, requiredSP);
															updatedSkillListHtml.append("<tr>").append("<td align=center>").append("<table border=0 cellpadding=0 cellspacing=0 width=310 height=40 background=\"").append(bgcolor).append("\">").append("<tr>").append("<td width=32 align=center valign=middle><img src=\"").append(updatedSkill.getIcon()).append("\" width=32 height=32></td>").append("<td width=100 align=left valign=middle>").append(updatedSkill.getName()).append(" (Lv ").append(updatedSkill.getLevel()).append(") ").append(spInfo).append("</td>").append("<td width=50 align=right valign=middle><button value=\" \" action=\"bypass _bbsgveskilllearn_").append(updatedSkillLearn.getSkillId()).append("_").append(updatedSkillLearn.getSkillLevel()).append("_").append(starLevel).append("\" width=32 height=32 back=\"L2UI_CT1.MiniMap_DF_PlusBtn_Gray_Down\" fore=\"L2UI_CT1.MiniMap_DF_PlusBtn_Gray\"/></td>").append("</tr>").append("</table>").append("</td>").append("</tr>");
														}
													}
													updatedSkillListHtml.append("<tr><td align=center><br>");
													if (updatedCurrentPage > 1) {
														updatedSkillListHtml.append("<button value=\"Prev\" action=\"bypass _bbsgveskill ").append(updatedCurrentPage - 1).append("\" width=60 height=25 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
													}
													updatedSkillListHtml.append(" Page ").append(updatedCurrentPage).append(" of ").append(updatedTotalPages);
													if (updatedCurrentPage < updatedTotalPages) {
														updatedSkillListHtml.append("<button value=\"Next\" action=\"bypass _bbsgveskill ").append(updatedCurrentPage + 1).append("\" width=60 height=25 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
													}
													updatedSkillListHtml.append("</td></tr>");
												} else {
													updatedSkillListHtml.append("<tr><td align=center><font color=FF0000>No GvE skills available for your class.</font></td></tr>");
												}
												dialog = dialog.replace("%skill_list%", updatedSkillListHtml.toString());
												dialog = dialog.replace("%gve_skill_points%", String.valueOf(player.getGveSkillPoints()));
												dialog = replaceVars(player, dialog);
												player.sendPacket(new ExPremiumManagerShowHtml(dialog));
											}
										} else {
											player.sendMessage("Error: Skill data not available!");
										}
									} else {
										player.sendMessage("Failed to deduct GvE Skill Points!");
									}
								} else {
									player.sendPacket(new SystemMessage(SystemMessageId.YOU_NEED_S1_GVE_SKILL_POINTS).addInt(skillLearn.getGveSkillPointsCost()));
								}
							} else {
								player.sendMessage("Skill not found in GvE skill tree!");
							}
						} catch (NumberFormatException e) {
							player.sendMessage("Invalid skill ID, level, or star level!");
						}
					} else {
						LOGGER.warning("Invalid command format for _bbsgveskilllearn: baseCommand=" + baseCommand + ", tokens remaining=" + (tokens.length - tokenIndex) + ", command=" + command);
					}
				}
				// Xử lý các lệnh Drop Calculator
				if (Config.ENABLE_DROP_CALCULATOR) {
					StringTokenizer st = new StringTokenizer(command, "_");
					st.nextToken(); // Bỏ qua phần "_bbs"
					if (command.startsWith("_bbssearchdropCalc")) {
						DropInfoBBSManager.getInstance().showMainPage(player);
						return true;
					} else if (command.startsWith("_bbssearchdropItemsByName_")) {
						if (!st.hasMoreTokens()) {
							player.sendMessage("Item name is missing.");
							DropInfoBBSManager.getInstance().showMainPage(player);
							return true;
						}
						String itemName = st.nextToken().trim();
						int itemsPage = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
						int sortMethod = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
						returnHtml = DropInfoBBSManager.getInstance().showDropItemsByNamePage(player, itemName, itemsPage, sortMethod);
					} else if (command.startsWith("_bbssearchdropMonstersByItem_")) {
						if (!st.hasMoreTokens()) {
							player.sendMessage("Item ID is missing.");
							return true;
						}
						int itemId = Integer.parseInt(st.nextToken());
						int monstersPage = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
						int sortMethod = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
						returnHtml = DropInfoBBSManager.getInstance().showDropMonstersByItem(player, itemId, monstersPage, sortMethod);
					} else if (command.startsWith("_bbssearchdropMonsterDetailsByItem_")) {
						if (!st.hasMoreTokens()) {
							player.sendMessage("Monster ID is missing.");
							return true;
						}
						int monsterId = Integer.parseInt(st.nextToken());
						returnHtml = DropInfoBBSManager.getInstance().showdropMonsterDetailsByItem(player, monsterId);
						CommunityBoardHandler.separateAndSend(returnHtml, player);
						if (st.hasMoreTokens()) {
							DropInfoBBSManager.getInstance().manageButton(player, Integer.parseInt(st.nextToken()), monsterId);
						}
						return true;
					} else if (command.startsWith("_bbssearchdropMonstersByName_")) {
						if (!st.hasMoreTokens()) {
							player.sendMessage("Monster name is missing.");
							DropInfoBBSManager.getInstance().showMainPage(player);
							return true;
						}
						String monsterName = st.nextToken().trim();
						int monsterPage = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
						int sortMethod = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
						returnHtml = DropInfoBBSManager.getInstance().showDropMonstersByName(player, monsterName, monsterPage, sortMethod);
					} else if (command.startsWith("_bbssearchdropMonsterDetailsByName_")) {
						if (!st.hasMoreTokens()) {
							player.sendMessage("Monster ID is missing.");
							return true;
						}
						int chosenMobId = Integer.parseInt(st.nextToken());
						returnHtml = DropInfoBBSManager.getInstance().showDropMonsterDetailsByName(player, chosenMobId);
						CommunityBoardHandler.separateAndSend(returnHtml, player);
						if (st.hasMoreTokens()) {
							DropInfoBBSManager.getInstance().manageButton(player, Integer.parseInt(st.nextToken()), chosenMobId);
						}
						return true;
					} else if (command.startsWith("_bbssearchNpcDropList")) {
						String[] partsDropList = command.split("_");
						if (partsDropList.length < 5) {
							player.sendMessage("Invalid drop list command format.");
							return true;
						}
						player.getVariables().set("DCDropType", partsDropList[2]);
						DropInfoFunctions.showNpcDropList(player, partsDropList[2], Integer.parseInt(partsDropList[3]), Integer.parseInt(partsDropList[4]));
						return true;
					} else if (command.startsWith("_bbssearchShowSkills")) {
						String[] partsSkills = command.split("_");
						if (partsSkills.length < 4) {
							player.sendMessage("Invalid show skills command format.");
							return true;
						}
						DropInfoFunctions.showNpcSkillList(player, Integer.parseInt(partsSkills[2]), Integer.parseInt(partsSkills[3]));
						return true;
					}
				}
				if (returnHtml != null) {
					CommunityBoardHandler.separateAndSend(returnHtml, player);
				}
				return true;
			}
		}

		return false;
	}

	
	private String online(boolean off)
	{
		int i = Rnd.get(1, 3);
		int j = 0;
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			i++;
			if (!player.isFakePlayer() && player.isInOfflineMode())
			{
				j++;
			}
		}
		return Util.formatAdena(!off ? (i + j) : j);
	}
	
	private static final DateFormat TIME_FORMAT = new SimpleDateFormat("HH:mm");
	
	public static String time()
	{
		return TIME_FORMAT.format(new Date(System.currentTimeMillis()));
	}
	
	public static String getOnlineTime(PlayerInstance player)
	{
		long total = player.getOnlineTime() + ((System.currentTimeMillis() / 1000) - player.getOnlineBeginTime());
		long days = (total / (60 * 60 * 24)) % 7;
		long hours = ((total - TimeUnit.DAYS.toSeconds(days)) / (60 * 60)) % 24;
		long minutes = (total - TimeUnit.DAYS.toSeconds(days) - TimeUnit.HOURS.toSeconds(hours)) / 60;
		if (days >= 1)
		{
			return days + " d. " + hours + " h. " + minutes + " min";
		}
		return hours + " hours " + player.getOnlineTime();
	}
	
	public String getServerRunTime()
	{
		int timeSeconds = GameTimeController.getInstance().getServerRunTime();
		String timeResult = "";
		if (timeSeconds >= 86400)
		{
			timeResult = Integer.toString(timeSeconds / 86400) + " Days " + Integer.toString((timeSeconds % 86400) / 3600) + " hours";
		}
		else
		{
			timeResult = Integer.toString(timeSeconds / 3600) + " Hours " + Integer.toString((timeSeconds % 3600) / 60) + " mins";
		}
		return timeResult;
	}
	
	private static String setSecretCode(PlayerInstance player, String action)
	{
		if (action.contains("\n"))
		{
			return "Error: Do not press Enter";
		}
		final String[] msg = action.split(" ");
		if ((msg.length < 3) || (msg.length > 5))
		{
			return "Either you didn't fill in a blank or you have spaces in your code";
		}
		if (msg[0].equals("_bbssetsecret_action"))
		{
			if (msg.length < 3)
			{
				return "You must enter both secret codes";
			}
			if (!msg[1].equals(msg[2]))
			{
				return "You retyped your secret code wrong";
			}
			if (!checkSecretCode(player, msg[1]))
			{
				return "Incorrect secret code format";
			}

			// Save secret code
			player.setSecretCodeAccount(msg[1]);
		}
		else if (msg[0].equals("_bbschangesecret_action"))
		{
			if (msg.length != 4)
			{
				return "You forgot to type in one of the prompts";
			}
			if (!msg[2].equals(msg[3]))
			{
				return "You retyped your secret code wrong";
			}
			if (!checkSecretCodeFormat(player, msg[2]))
			{
				return "Incorrect secret code format";
			}
			if (!player.getSecretCode().equals(msg[1]))
			{
				return "Incorrect account secret code";
			}
			player.setSecretCodeAccount(msg[2]);
		}
		else
		{
			LOGGER.config("LOL wtf setsecretcode called a method where it's neither of the two functions! user name: " + player.getName());
		}
		return null;
	}
	
	private static boolean checkSecretCode(PlayerInstance player, String secret)
	{
		if ((secret == null) || secret.isEmpty())
		{
			return false;
		}
		secret = secret.trim();
		if ((secret == null) || secret.isEmpty() || secret.equalsIgnoreCase("") || secret.contains(" "))
		{
			return false;
		}
		if ((secret.length() < 2) || (secret.length() > 20))
		{
			return false;
		}
		if (player.getSecretCode() != null)
		{
			if (!secretCodeOk(player, secret))
			{
				return false;
			}
		}
		return true;
	}
	
	private static boolean checkSecretCodeFormat(PlayerInstance player, String secret)
	{
		if ((secret == null) || secret.isEmpty())
		{
			return false;
		}
		secret = secret.trim();
		if ((secret == null) || secret.isEmpty() || secret.equalsIgnoreCase("") || secret.contains(" "))
		{
			return false;
		}
		if ((secret.length() < 2) || (secret.length() > 20))
		{
			return false;
		}
		return true;
	}
	
	private static boolean secretCodeOk(PlayerInstance player, String secret)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("SELECT secret FROM accounts WHERE login = ?"))
		{
			statement.setString(1, player.getAccountName());
			try (ResultSet rset = statement.executeQuery())
			{
				if (rset.next())
				{
					if (rset.getString("secret").equals(secret))
					{
						// player.sendMessage("Wrong secret code.");
						return true;
					}
					return false;
				}
			}
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
		return true;
	}
	
	private static String doPasswordChange(PlayerInstance player, String action)
	{
		if (action.contains("\n"))
		{
			return "Error: Do not press Enter";
		}
		final String[] msg = action.split(" ", 3);
		if (msg.length < 3)
		{
			return "You need to type in both your secret code and your new password";
		}
		final String secret = msg[1];
		if (!checkSecretCode(player, secret))
		{
			return "Incorrect secret code";
		}
		final String password = msg[2];
		if (password.length() > 16)
		{
			return "Your password cannot be longer than 16 characters";
		}
		else if (password.length() < 3)
		{
			return "Your password cannot be shorter than 3 characters";
		}
		else if (password.startsWith(" "))
		{
			return "Your password cannot start with spaces";
		}
		String auth = null;
		try
		{
			final MessageDigest md = MessageDigest.getInstance("SHA");
			final byte[] newPassword = md.digest(password.getBytes("UTF-8"));
			final String accName = player.getAccountName();
			boolean authed = false;
			Connection con = null;
			try
			{
				con = DatabaseFactory.getConnection();
				PreparedStatement statement = con.prepareStatement("SELECT secret FROM accounts WHERE login = ?");
				statement.setString(1, accName);
				try (ResultSet rset = statement.executeQuery())
				{
					if (rset.next())
					{
						if (rset.getString("secret").equals(secret))
						{
							authed = true;
						}
						else
						{
							auth = "Incorrect input";
						}
					}
					rset.close();
					statement.close();
					if (authed)
					{
						statement = con.prepareStatement("UPDATE accounts SET password = ?, pass = ? WHERE login = ?");
						statement.setString(1, Base64.getEncoder().encodeToString(newPassword));
						statement.setString(2, password);
						statement.setString(3, accName);
						statement.executeUpdate();
						player.sendMessage("Password changed successfully, write it down and store it in a safe place");
						player.getClient().setPassword(password);
					}
					else
					{
						player.sendMessage("Wrong secret question");
					}
					rset.close();
					statement.close();
				}
			}
			catch (SQLException e)
			{
				e.printStackTrace();
			}
			finally
			{
				try
				{
					con.close();
				}
				catch (Exception e)
				{}
			}
		}
		catch (Exception e)
		{
			player.sendMessage("There was an error with your password change.");
			e.printStackTrace();
		}
		return auth;
	}
	
	private static boolean isSecretCodeConfirmed(PlayerInstance player, String action)
	{
		if (action.contains("\n"))
		{
			player.sendMessage("Do not press enter.");
			return false;
		}
		final String[] msg = action.split(" ", 2);
		if (msg.length < 2)
		{
			player.sendMessage("WTF dude are you trying to break the server?");
			return false;
		}
		final String secret = msg[1];
		if (!checkSecretCode(player, secret))
		{
			player.sendMessage("Incorrect secret code");
			return false;
		}
		return true;
	}
	
	private String formatStatName(String name)
	{
		String[] words = name.split("_");
		StringBuilder formattedName = new StringBuilder();
		for (String word : words)
		{
			if (word.length() > 0)
			{
				formattedName.append(word.substring(0, 1).toUpperCase()).append(word.substring(1).toLowerCase()).append(" ");
			}
		}
		return formattedName.toString().trim();
	}
	
	private static String setTimeLockDown(PlayerInstance player, String action)
	{
		if (action.contains("\n"))
		{
			return "Error: Do not press Enter";
		}
		final String[] msg = action.split(" ");
		if ((msg.length < 1) || (msg.length > 2))
		{
			return "Either you didn't fill in a blank or you have spaces in your code";
		}
		if (msg[0].equals("_bbstop_settings_player_block_down_504"))
		{
			if (msg.length != 2)
			{
				return "You cannot have spaces in your secret code";
			}
			double timeInHours;
			try
			{
				timeInHours = Double.valueOf(msg[1]);
			}
			catch (NumberFormatException e)
			{
				return "Invalid input: Please enter a valid number (e.g., 0.1, 24, 504)";
			}
			if ((timeInHours < 0.1) || (timeInHours > 504))
			{
				return "It's a minimum of 0.1 and a maximum of 504 hours";
			}
			player.doLockdown(timeInHours);
		}
		else
		{
			LOGGER.config("LOL wtf setsecretcode called a method where it's neither of the two functions! user name: " + player.getName());
		}
		return null;
	}
	
	private String formatPercent(double value)
	{
		return String.format("%.0f%%", value);
	}

	/**
	 * Replace giftcode-specific variables in HTML
	 */
	private String replaceGiftcodeVars(PlayerInstance player, String html) {
		if (html == null) {
			return null;
		}

		// Basic player info
		html = html.replace("%account_name%", player.getAccountName());
		html = html.replace("%prime_points%", String.valueOf(player.getPrimePoints()));

		// Check if account has used giftcode
		boolean hasUsedGiftcode = hasAccountUsedGiftcode(player.getAccountName());
		html = html.replace("%giftcode_status%", hasUsedGiftcode ? "Đã sử dụng" : "Chưa sử dụng");
		html = html.replace("%giftcode_status_color%", hasUsedGiftcode ? "FF6666" : "66FF66");

		// Premium status
		boolean hasPremium = player.hasPremiumStatus();
		html = html.replace("%premium_status%", hasPremium ? "Đang hoạt động" : "Không hoạt động");
		html = html.replace("%premium_status_color%", hasPremium ? "66FF66" : "FF6666");

		// Apply other standard replacements
		html = replaceVars(player, html);

		return html;
	}

	/**
	 * Check if account has used any giftcode
	 */
	private boolean hasAccountUsedGiftcode(String accountName) {
		try (Connection con = DatabaseFactory.getConnection();
			 PreparedStatement ps = con.prepareStatement("SELECT COUNT(*) FROM giftcodes WHERE used_by_account = ?")) {

			ps.setString(1, accountName);
			try (ResultSet rs = ps.executeQuery()) {
				if (rs.next()) {
					return rs.getInt(1) > 0;
				}
			}
		} catch (SQLException e) {
			LOGGER.log(Level.WARNING, "Error checking giftcode usage: " + e.getMessage(), e);
		}
		return false;
	}

	/**
	 * Get the best (highest) share rate from all premium sharing players in party
	 * @param party The party to check
	 * @return The highest share rate found, or 50 if no sharing players
	 */
	private int getBestShareRateInParty(Party party)
	{
		if (party == null)
		{
			return 50; // Default rate
		}

		int bestRate = 0;
		for (PlayerInstance member : party.getMembers())
		{
			if (member != null && member.hasPremiumStatus() &&
				member.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false))
			{
				int memberRate = member.getVariables().getInt("PREMIUM_SHARE_RATE", 50);
				bestRate = Math.max(bestRate, memberRate);
			}
		}

		return bestRate > 0 ? bestRate : 50; // Return 50 if no sharing players found
	}
	
	private String formatDuration(int seconds)
	{
		long days = seconds / (24 * 3600);
		seconds %= (24 * 3600);
		long hours = seconds / 3600;
		seconds %= 3600;
		long minutes = seconds / 60;
		if (days > 0)
		{
			return String.format("%d days %d hours %d minutes", days, hours, minutes);
		}
		else if (hours > 0)
		{
			return String.format("%d hours %d minutes", hours, minutes);
		}
		else
		{
			return String.format("%d minutes", minutes);
		}
	}
	
	private String replaceVars(PlayerInstance activeChar, String content)
	{
		String html = content;
		// Player name with faction color and info
		String playerName = activeChar.getName();
		String factionInfo = "";
		String nameColor = "FFFFFF"; // Default white

		if (activeChar.getFaction() != null && activeChar.getFaction() != Faction.NONE)
		{
			if (activeChar.getFaction() == Faction.FIRE)
			{
				nameColor = "FF0000"; // Red for Fire
				factionInfo = " [Fire]";
			}
			else if (activeChar.getFaction() == Faction.WATER)
			{
				nameColor = "48BEFA"; // Blue for Water
				factionInfo = " [Water]";
			}
		}

		String coloredName = "<font color=\"" + nameColor + "\">" + playerName + factionInfo + "</font>";
		html = html.replace("%name%", coloredName);
		html = html.replace("%class%", ClassListData.getInstance().getClass(activeChar.getClassId()).getClientCode());
		html = html.replace("%level%", String.valueOf(activeChar.getLevel()));
		html = html.replace("%clan_name%", String.valueOf(activeChar.getClan() != null ? activeChar.getClan().getName() : "No"));
		boolean hasPremiumStatus = activeChar.hasPremiumStatus();
		// Use base premium expiration (already includes consumed time from database)
		long premiumExpiration = hasPremiumStatus ? PremiumManager.getInstance().getPremiumExpiration(activeChar.getObjectId()) : 0;
		final String premiumDuration = hasPremiumStatus ? new SimpleDateFormat("dd/MM HH:mm:ss").format(premiumExpiration) : "N/A";

		// Debug premium duration values
		if (activeChar.getVariables().getBoolean("DEBUG", false) && hasPremiumStatus)
		{
			long realTimeExpiration = PremiumManager.getInstance().getRealTimePremiumExpiration(activeChar.getObjectId());
		}
		html = html.replace("%PREMIUM_STATUS%", String.valueOf(hasPremiumStatus ? "Yes" : "N/A"));
		html = html.replace("%PREMIUM_DURATION%", premiumDuration);

		// Share Premium status
		boolean sharePremiumEnabled = activeChar.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false);
		String sharePremiumIconStatus = (hasPremiumStatus && sharePremiumEnabled) ? "ONICON" : "OffICON";
		html = html.replace("%share_premium_status%", sharePremiumIconStatus);

		// Share Premium rate
		int shareRate = activeChar.getVariables().getInt("PREMIUM_SHARE_RATE", 50);
		html = html.replace("%share_premium_rate%", String.valueOf(shareRate));

		// Add sharing duration info
		String sharingInfo = "";
		if (hasPremiumStatus && sharePremiumEnabled)
		{
			// Only show active sharing if player is in party and has tracking
			if (activeChar.getParty() != null)
			{
				long startTime = activeChar.getVariables().getLong("PREMIUM_SHARING_START_TIME", 0);
				long sharingDuration = PremiumManager.getInstance().getCurrentSharingDuration(activeChar.getObjectId());
				long currentTime = System.currentTimeMillis();

				// Conditional debug info
				if (activeChar.getVariables().getBoolean("DEBUG", false))
				{
					System.out.println("DEBUG: startTime=" + startTime + ", currentTime=" + currentTime + ", sharingDuration=" + sharingDuration + ", party=" + (activeChar.getParty() != null));
					if (startTime > 0) {
						System.out.println("DEBUG: Raw duration calculation: " + (currentTime - startTime) + "ms");
					}
				}



				if (sharingDuration > 0)
				{
					// Show positive sharing duration (how long has been sharing) in HH:MM:SS format
					sharingInfo = " " + PremiumManager.formatSharingDuration(sharingDuration);
				}
				else
				{
					if (startTime > 0)
					{
						sharingInfo = "Tracking active but no duration";
					}
					else
					{
						sharingInfo = "Ready to start";
					}
				}
			}
			else
			{
				sharingInfo = "No party";
			}
		}
		else if (hasPremiumStatus)
		{
			sharingInfo = "Disabled";
		}
		else
		{
			sharingInfo = "N/A";
		}
		html = html.replace("%sharing_duration_info%", sharingInfo);

		// Shared Premium info (for people receiving shared premium)
		String sharedPremiumInfo = "N/A";
		boolean hasSharedPremium = activeChar.getVariables().getFloat("SHARED_PREMIUM_EXP_RATE", 0.0f) > 0.0f;

		if (hasSharedPremium)
		{
			float expRate = activeChar.getVariables().getFloat("SHARED_PREMIUM_EXP_RATE", 1.0f);
			float spRate = activeChar.getVariables().getFloat("SHARED_PREMIUM_SP_RATE", 1.0f);
			float adenaChance = activeChar.getVariables().getFloat("SHARED_PREMIUM_ADENA_CHANCE", 1.0f);
			float dropChance = activeChar.getVariables().getFloat("SHARED_PREMIUM_DROP_CHANCE", 1.0f);

			sharedPremiumInfo = String.format("<font color=00FF00>Active</font><br1>EXP: %.1fx, SP: %.1fx<br1>Adena: %.2fx, Drop: %.2fx",
				expRate, spRate, adenaChance, dropChance);
		}
		else if (hasPremiumStatus)
		{
			sharedPremiumInfo = "<font color=FFFF00>Individual Premium</font>";
		}
		else
		{
			sharedPremiumInfo = "<font color=999999>None</font>";
		}

		html = html.replace("{shared_premium_info}", sharedPremiumInfo);
		// Thêm hiển thị GvE Skill Points
		html = html.replace("%gve_skill_points%", String.valueOf(activeChar.getGveSkillPoints()));
		// Khởi tạo các biến Premium với giá trị mặc định nếu không có Premium hoặc giá trị là null
		if (!hasPremiumStatus)
		{
			activeChar.getVariables().set(PlayerVariables.PREMIUM_EXP_RATE, 0.0f);
			activeChar.getVariables().set(PlayerVariables.PREMIUM_SP_RATE, 0.0f);
			activeChar.getVariables().set(PlayerVariables.PREMIUM_ADENA_RATE, 0.0f); // Legacy
			activeChar.getVariables().set(PlayerVariables.PREMIUM_ADENA_CHANCE, 0.0f);
			activeChar.getVariables().set(PlayerVariables.PREMIUM_ADENA_AMOUNT, 0.0f);
			activeChar.getVariables().set(PlayerVariables.PREMIUM_DROP_CHANCE, 0.0f);
			activeChar.getVariables().set(PlayerVariables.PREMIUM_DROP_AMOUNT, 0.0f);
			activeChar.getVariables().set(PlayerVariables.PREMIUM_SPOIL_CHANCE, 0.0f);
			activeChar.getVariables().set(PlayerVariables.PREMIUM_SPOIL_AMOUNT, 0.0f);
			activeChar.getVariables().set(PlayerVariables.PREMIUM_SAYHA_GRACE_XP, 0.0f);
			activeChar.getVariables().set(PlayerVariables.PREMIUM_LIMITED_SAYHA_GRACE_XP, 0.0f);
			activeChar.getVariables().set(PlayerVariables.PREMIUM_RESURRECTION_COST, 0.0f);
			activeChar.getVariables().set(PlayerVariables.PREMIUM_RANDOM_CRAFT_HERB, 0.0f);
			activeChar.getVariables().set(PlayerVariables.PREMIUM_LCOIN_DROP_AMOUNT_ADD, 0.0f);
			activeChar.getVariables().set(PlayerVariables.PREMIUM_ONLY_FISHING, false);
		}
		int premiumId = PremiumManager.getInstance().getPremiumId(activeChar.getObjectId());
		PremiumAccount premium = PremiumData.getInstance().getPremium(premiumId);
		String premiumName = (premium != null) ? premium.getName() : "None";
		html = html.replace("%Premium_Name%", premiumName);
		Map<Integer, Long> data = DropManager.getInstance().getPassiveItemData(activeChar.getAccountName(), LCOIN);
		html = html.replace("%lcoin_farm%", String.valueOf(data.get(LCOIN)));
		html = html.replace("%max_lcoin_farm%", String.valueOf(DropManager.getInstance().getMaxDrop(activeChar, LCOIN)));



		final int vipTier = activeChar.getVipTier();
		final String vipDuration = new SimpleDateFormat("dd/MM HH:mm:ss").format(activeChar.getVipTierExpiration());
		html = html.replace("%VIP%", String.valueOf(vipTier > 0 ? vipTier : "0"));
		html = html.replace("%VIP_Duration%", String.valueOf(vipTier > 0 ? vipDuration : "N/A"));
		html = html.replace("%VIP_Point%", String.valueOf(activeChar.getVipPoints()));
		long pointsRequired = VipSystemManager.getInstance().getPointsToLevel(vipTier + 1);
		html = html.replace("%VIP_Point_Next_Level%", String.valueOf(vipTier > 0 ? (pointsRequired - activeChar.getVipPoints()) : "0"));
		final SimpleDateFormat format = new SimpleDateFormat("mm:ss");
		html = html.replace("%online_time%", format.format(activeChar.getCurrentOnlineTime()));
		html = html.replace("%ip%", activeChar.getIPAddress());
		html = html.replace("%server_uptime%", getServerRunTime());
		html = html.replace("%time%", String.valueOf(time()));
		html = html.replace("%online%", online(false));
		html = html.replace("%offtrade%", online(true));
		html = html.replace("%xp_rate%", Float.toString(Config.RATE_XP));
		html = html.replace("%sp_rate%", Float.toString(Config.RATE_SP));
		html = html.replace("%party_xp_rate%", Float.toString(Config.RATE_PARTY_XP));
		html = html.replace("%party_sp_rate%", Float.toString(Config.RATE_PARTY_SP));
		Float adenaChance = Config.RATE_DROP_CHANCE_BY_ID.get(Inventory.ADENA_ID);
		html = html.replace("%adena_chance%", Float.toString(adenaChance));
		Float dropChance = Config.RATE_DEATH_DROP_CHANCE_MULTIPLIER;
		html = html.replace("%drop_chance%", Float.toString(dropChance));
		html = html.replace("%raid_chance%", "1.0");
		Float spoilChance = Config.RATE_SPOIL_DROP_CHANCE_MULTIPLIER;
		html = html.replace("%spoil_chance%", Float.toString(spoilChance));
		html = html.replace("%adena_amount%", "1.0");
		Float dropAmount = Config.RATE_DEATH_DROP_AMOUNT_MULTIPLIER;
		html = html.replace("%drop_amount%", Float.toString(dropAmount));
		Float raidAmount = Config.RATE_RAID_DROP_AMOUNT_MULTIPLIER;
		html = html.replace("%raid_amount%", Float.toString(raidAmount));
		Float spoilAmount = Config.RATE_SPOIL_DROP_AMOUNT_MULTIPLIER;
		html = html.replace("%spoil_amount%", Float.toString(spoilAmount));
		html = html.replace("%quest_drop%", Float.toString(Config.RATE_QUEST_DROP));
		html = html.replace("%quest_reward_xp%", Float.toString(Config.RATE_QUEST_REWARD_XP));
		html = html.replace("%quest_reward_sp%", Float.toString(Config.RATE_QUEST_REWARD_SP));
		html = html.replace("%quest_reward%", Float.toString(Config.RATE_QUEST_REWARD));
		html = html.replace("%quest_reward_adena%", Float.toString(Config.RATE_QUEST_REWARD_ADENA));
		html = html.replace("%max_client%", "1");
		html = html.replace("%enchant_safe_max%", "+4/+20");
		html = html.replace("%buff_dance_slot%", Float.toString(Config.BUFFS_MAX_AMOUNT));
		html = html.replace("%buff_time%", "LifeTime");
		html = html.replace("%hide_skill_animation_status%", activeChar.getVariables().getBoolean("HIDE_SKILL_ANIMATION", true) ? "OffICON" : "ONICON");

		// Discord status replacement
		final PlayerVariables vars = activeChar.getVariables();
		boolean isVerified = vars.getBoolean(PlayerVariables.DISCORD_IS_NAME_VERIFIED, false);
		String discordName = vars.getString(PlayerVariables.DISCORD_NAME, "");
		String discordStatus;

		if (isVerified)
		{
			discordStatus = "<font color=\"00FF00\">✓ Verified (" + discordName + ")</font>";
		}
		else
		{
			discordStatus = "<font color=\"FF0000\">✗ Not verified</font>";
		}
		html = html.replace("%discord_status%", discordStatus);

		// Auto-resurrection status
		boolean autoResEnabled = activeChar.getAutoPlaySettings().isAutoResurrectionEnabled(activeChar);
		String autoResIconStatus = autoResEnabled ? "ONICON" : "OffICON";
		html = html.replace("%auto_resurrection_status%", autoResIconStatus);

		// Auto-resurrection usage info
		int usesToday = activeChar.getAutoPlaySettings().getAutoResurrectionUsesToday(activeChar);
		int maxUses = activeChar.hasPremiumStatus() ?
			club.projectessence.Config.AUTOPLAY_AUTO_RESURRECTION_MAX_USES_PER_DAY_PREMIUM :
			club.projectessence.Config.AUTOPLAY_AUTO_RESURRECTION_MAX_USES_PER_DAY;

		html = html.replace("%auto_res_uses_today%", usesToday + "/" + maxUses);
		html = html.replace("%auto_res_premium_status%", activeChar.hasPremiumStatus() ? "Premium" : "Regular");

		// Auto-resurrection cooldown status
		long lastResTime = activeChar.getAutoPlaySettings().getLastAutoResurrectionTime(activeChar);
		String cooldownStatus = "Ready";
		if (lastResTime > 0) {
			long timeSince = (System.currentTimeMillis() - lastResTime) / 1000;
			long cooldownRemaining = club.projectessence.Config.AUTOPLAY_AUTO_RESURRECTION_COOLDOWN - timeSince;
			if (cooldownRemaining > 0) {
				cooldownStatus = cooldownRemaining + "s remaining";
			}
		}
		html = html.replace("%auto_res_cooldown%", cooldownStatus);

		return html;
	}

	// Discord Recovery Helper Methods

	private static boolean sendRecoveryDiscord(PlayerInstance player)
	{
		// Check if player has Discord verified
		final PlayerVariables vars = player.getVariables();
		boolean isVerified = vars.getBoolean(PlayerVariables.DISCORD_IS_NAME_VERIFIED, false);
		String discordName = vars.getString(PlayerVariables.DISCORD_NAME, "");

		if (!isVerified)
		{
			player.sendMessage("You need to verify your Discord account first!");
			player.sendMessage("Use the Discord verification system in game to link your account.");
			return false;
		}

		// Check cooldown - prevent spam (5 minutes cooldown)
		long lastRecoveryTime = vars.getLong("LAST_RECOVERY_REQUEST", 0);
		long currentTime = System.currentTimeMillis();
		long cooldownTime = 5 * 60 * 1000; // 5 minutes in milliseconds

		if (currentTime - lastRecoveryTime < cooldownTime)
		{
			long remainingTime = (cooldownTime - (currentTime - lastRecoveryTime)) / 1000;
			player.sendMessage("Please wait " + remainingTime + " seconds before requesting another recovery code.");
			return false;
		}

		// Generate 6-digit recovery code
		String recoveryCode = String.format("%06d", Rnd.get(100000, 999999));

		// Save recovery token to database AND set as new secret code
		if (!saveDiscordRecoveryTokenAndSetSecret(player.getAccountName(), discordName, recoveryCode))
		{
			return false;
		}

		// Update last recovery request time
		vars.set("LAST_RECOVERY_REQUEST", currentTime);

		// Cleanup old expired tokens (run occasionally)
		cleanupExpiredTokens();

		// Send Discord DM
		String message = "🔐 **L2GVE Secret Code Recovery**\n\n" +
						"**Character:** " + player.getName() + "\n" +
						"**New Secret Code:** " + recoveryCode + "\n\n" +
						"✅ **This code has been automatically set as your new secret code!**\n" +
						"⚠️ **Important:** You can change this to a different secret code later for better security.\n\n" +
						"If you did not request this, please contact an administrator immediately.";

		try
		{
			DiscordBotManager.getInstance().sendMessageToPlayer(player, message);
			return true;
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Error sending Discord recovery message", e);
			return false;
		}
	}

	private static boolean saveDiscordRecoveryTokenAndSetSecret(String accountName, String discordName, String token)
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			// Start transaction
			con.setAutoCommit(false);

			try
			{
				// Save recovery token
				try (PreparedStatement ps1 = con.prepareStatement("INSERT INTO discord_recovery_tokens (account_name, discord_id, token, expiry_time) VALUES (?, ?, ?, DATE_ADD(NOW(), INTERVAL 30 MINUTE))"))
				{
					ps1.setString(1, accountName);
					ps1.setString(2, discordName);
					ps1.setString(3, token);
					ps1.executeUpdate();
				}

				// Set as new secret code immediately
				try (PreparedStatement ps2 = con.prepareStatement("UPDATE accounts SET secret = ? WHERE login = ?"))
				{
					ps2.setString(1, token);
					ps2.setString(2, accountName);
					ps2.executeUpdate();
				}

				// Commit transaction
				con.commit();
				return true;
			}
			catch (SQLException e)
			{
				// Rollback on error
				con.rollback();
				throw e;
			}
			finally
			{
				con.setAutoCommit(true);
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.SEVERE, "Error saving Discord recovery token and setting secret code", e);
			return false;
		}
	}






	/**
	 * Cleanup expired recovery tokens from database
	 * Only runs occasionally to avoid performance impact
	 */
	private static void cleanupExpiredTokens()
	{
		// Only run cleanup 10% of the time to avoid performance impact
		if (Rnd.get(100) < 10)
		{
			try (Connection con = DatabaseFactory.getConnection();
				 PreparedStatement ps = con.prepareStatement("DELETE FROM discord_recovery_tokens WHERE expiry_time < NOW()"))
			{
				int deletedRows = ps.executeUpdate();
				if (deletedRows > 0)
				{
					LOGGER.info("Cleaned up " + deletedRows + " expired Discord recovery tokens");
				}
			}
			catch (SQLException e)
			{
				LOGGER.log(Level.WARNING, "Error cleaning up expired Discord recovery tokens", e);
			}
		}
	}

/**
 * Format rate as percentage for display
 * @param rate The rate value (e.g., 1.15 for 15% bonus)
 * @return Formatted percentage string (e.g., "+15%" or "+0%")
 */
private String formatRateAsPercentage(float rate)
{
	if (rate >= 1.0f)
	{
		// Convert rate to percentage (1.15 -> +15%)
		int percentage = Math.round((rate - 1.0f) * 100);
		return "+" + percentage + "%";
	}
	else
	{
		// For rates less than 1.0 (like resurrection cost 0.5 -> -50%)
		int percentage = Math.round((1.0f - rate) * 100);
		return "-" + percentage + "%";
	}

	// End of parseCommunityBoardCommand method
	}
}